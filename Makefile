# Makefile for Object Detection Service

# Variables
PYTHON := python3
PIP := pip
DOCKER := docker
DOCKER_COMPOSE := docker-compose
TEST_VIDEO := test_video.mp4

# Default target
.PHONY: help
help:
	@echo "Object Detection Service - Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make install           Install dependencies"
	@echo "  make setup-check       Check if setup is correct"
	@echo "  make run               Run the FastAPI application"
	@echo "  make worker            Run the Celery worker"
	@echo "  make docker-up         Start services with Docker Compose"
	@echo "  make docker-down       Stop services with Docker Compose"
	@echo "  make docker-logs       View Docker Compose logs"
	@echo "  make test-setup        Test the setup"
	@echo "  make test-video        Test video processing"
	@echo "  make test              Run all tests"
	@echo "  make clean             Clean temporary files"

# Install dependencies
.PHONY: install
install:
	$(PIP) install -r requirements.txt

# Check setup
.PHONY: setup-check
setup-check:
	$(PYTHON) test_setup.py

# Run FastAPI application
.PHONY: run
run:
	$(PYTHON) -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Run Celery worker
.PHONY: worker
worker:
	$(PYTHON) -m worker.worker

# Start services with Docker Compose
.PHONY: docker-up
docker-up:
	$(DOCKER_COMPOSE) up -d

# Stop services with Docker Compose
.PHONY: docker-down
docker-down:
	$(DOCKER_COMPOSE) down

# View Docker Compose logs
.PHONY: docker-logs
docker-logs:
	$(DOCKER_COMPOSE) logs -f

# Test the setup
.PHONY: test-setup
test-setup:
	$(PYTHON) test_setup.py

# Test video processing
.PHONY: test-video
test-video:
	$(PYTHON) test_video_processing.py

# Run all tests
.PHONY: test
test: test-setup test-video

# Clean temporary files
.PHONY: clean
clean:
	rm -rf logs/*.log*
	rm -rf tmp/
	rm -rf __pycache__/
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +

# Create test video
.PHONY: create-test-video
create-test-video:
	$(PYTHON) -c "import cv2; import numpy as np; width, height = 640, 480; fourcc = cv2.VideoWriter_fourcc(*'mp4v'); out = cv2.VideoWriter('$(TEST_VIDEO)', fourcc, 30, (width, height)); frame = np.zeros((height, width, 3), dtype=np.uint8); cv2.rectangle(frame, (100, 100), (300, 200), (0, 255, 0), -1); for _ in range(90): out.write(frame); out.release(); print('Test video created: $(TEST_VIDEO)')"