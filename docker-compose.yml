version: '3.8'

services:
  # RabbitMQ message broker
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: rabbitusr
      RABBITMQ_DEFAULT_PASS: 1lqgEJU3VPyhg
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL for job tracking
  postgresql:
    image: postgres:15
    container_name: postgresql
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "4GfW42eVb"
      POSTGRES_DB: "object_detection"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Main API service
  api:
    build:
      context: .
      dockerfile: DockerfileApi
    container_name: api
    ports:
      - "8000:8000"
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgresql:
        condition: service_healthy
    volumes:
      - ./known_faces:/app/known_faces
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Celery workers
  worker-1:
    build:
      context: .
      dockerfile: DockerfileWorker
    container_name: worker-1
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgresql:
        condition: service_healthy
    volumes:
      - ./known_faces:/app/known_faces

  worker-2:
    build:
      context: .
      dockerfile: DockerfileWorker
    container_name: worker-2
    depends_on:
      rabbitmq:
        condition: service_healthy
      postgresql:
        condition: service_healthy
    volumes:
      - ./known_faces:/app/known_faces

volumes:
  rabbitmq_data:
  postgresql_data: