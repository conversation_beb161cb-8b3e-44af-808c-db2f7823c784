version: '3.8'

services:
  # RabbitMQ message broker
  rabbitmq:
    image: rabbitmq:3-management
    container_name: object-detection-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MongoDB for job tracking
  mongodb:
    image: mongo:5.0
    container_name: object-detection-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    volumes:
      - mongodb_data:/data/db
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 5

  # Main API service
  api:
    build:
      context: .
      dockerfile: DockerfileApi
    container_name: object-detection-api
    ports:
      - "8000:8000"
    depends_on:
      rabbitmq:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    volumes:
      - ./known_faces:/app/known_faces
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Celery workers
  worker-1:
    build:
      context: .
      dockerfile: DockerfileWorker
    container_name: object-detection-worker-1
    depends_on:
      rabbitmq:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    volumes:
      - ./known_faces:/app/known_faces

  worker-2:
    build:
      context: .
      dockerfile: DockerfileWorker
    container_name: object-detection-worker-2
    depends_on:
      rabbitmq:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    volumes:
      - ./known_faces:/app/known_faces

volumes:
  rabbitmq_data:
  mongodb_data: