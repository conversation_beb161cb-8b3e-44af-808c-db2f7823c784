# Known Faces Directory

This directory contains reference images for face recognition.

## Structure

```
known_faces/
├── person1/
│   ├── photo1.jpg
│   ├── photo2.jpg
│   └── photo3.jpg
├── person2/
│   ├── photo1.jpg
│   └── photo2.jpg
└── encodings.json  # Auto-generated file containing face encodings
```

## How to Add Known Faces

1. Create a subdirectory for each person
2. Add reference images to each person's directory
3. The system will automatically generate encodings when it starts

## Image Requirements

- Images should be clear, front-facing photos
- Multiple images per person improve recognition accuracy
- Recommended minimum of 3-5 images per person
- Supported formats: JPEG, PNG