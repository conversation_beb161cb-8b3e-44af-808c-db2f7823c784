import time
import requests
import json
import uuid

def cloudmltest(mcount=4, videourl="", callbackurl="", posturl=""):
    for i in range(mcount):
        time.sleep(0.5)
        uuidval = str(uuid.uuid4())

        ppayload = {
            "uuid": uuidval,
            "video_url": videourl,
            "callback_url": callbackurl,
            "object_detection_enabled": True,
            "face_recognition_enabled": False,
            "license_plate_recognition_enabled": False,
            "barcode_recognition_enabled": False,
            "detected_image_export": False
        }

        payload = json.dumps(ppayload)
        headers = {
          'Content-Type': 'application/json',
          # 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.diFBNciJXP9VEBhNsa-1m_rO-oMKLLLl5HfVQu1rv2k'
        }

        response = requests.request("POST", posturl, headers=headers, data=payload, verify=True)

        print(i, response.text)


#cloudmltest(10,"http://127.0.0.1:8080/object_detection")
cloudmltest(50,"https://ezlo.uzay.space/static/video/melih.mp4","https://webhook.site/41acbdf3-f8c2-453d-a114-d99f316d945b","http://localhost:8000/detect")




# canceljobs()