# Default Configuration for Object Detection Service
# This configuration provides sensible defaults for production deployment

# RabbitMQ Configuration
rabbitmq:
  host: "rabbitmq"
  port: 5672
  username: "rabbitusr"
  password: "1lqgEJU3VPyhg"
  vhost: "/"

# PostgreSQL Configuration
postgresql:
  host: "postgresql"
  port: 5432
  username: "postgres"
  password: "4*Gf&W42eVb"
  database: "object_detection"
  echo: false

# Authentication Configuration
auth:
  enabled: false
  secret_key: "0968eb8c20c69225f15aa77d88d1552b4865b75a48ecc7289bd43c2293aac918829bfda206f38ae8ab669e6afad01c7f245f2c55bfe1c30f56e7ab67385dbb4a"
  algorithm: "HS256"

# YOLO Configuration
yolo:
  enabled: true
  model: "ezlomodels/ezlo-yolo-standard-11s.pt"
  detection_classes:
    - "person"
    - "car"
    - "truck"
    - "bus"
    - "motorcycle"
    - "bicycle"
    - "traffic light"
    - "stop sign"
  person_detection_classes:
    - "person"
  animal_detection_classes:
    - "cat"
    - "dog"
  vehicle_detection_classes:
    - "car"
    - "motorcycle"
  confidence_threshold: 0.5
  nms_threshold: 0.4
  process_every_n_frames: 5
  device: "cpu"
  skip_frame_ratio: 0.5

# Package Detection Configuration
package:
  enabled: true
  model: "ezlomodels/ezlo-yolowsv2.pt"
  person_detection_classes:
    - "person"
    - "parcel"
    - "box"
    - "bundle"
    - "packet"
    - "package"
    - "crate"
    - "carton"
    - "envelope"
    - "case"
    - "kit"
    - "bag"
  skip_frame_ratio: 0.5

# CustomPackage Detection Configuration
custom_package:
  enabled: true
  model: "ezlomodels/ezlo-custom-package.pt"

# Face Recognition Configuration
face_recognition:
  enabled: true
  model: "hog"
  tolerance: 0.6
  upsample_times: 1
  num_jitters: 1
  known_faces_directory: "known_faces"
  encodings_file: "known_faces/encodings.json"
  detect_faces_only: false
  process_every_n_frames: 10

# License Plate Recognition Configuration
license_plate_recognition:
  enabled: true
  model: "ezlomodels/license_plate_detector.pt"
  ocr_library: "easyocr"
  languages: ["en"]
  detection_model: "best"
  confidence_threshold: 0.69
  country: "us"
  whitelist_characters: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
  preprocess_enabled: true
  preprocess_options:
    grayscale: true
    blur: false
    threshold: true
  process_every_n_frames: 15
  license_plate_detection_classes:
    - "license plate"

# Barcode Recognition Configuration
barcode_recognition:
  enabled: true
  library: "pyzbar"
  formats: 
    - "QR_CODE"
    - "CODE128"
    - "EAN13"
    - "CODE39"
  confidence_threshold: 0.8
  preprocess_enabled: true
  preprocess_options:
    grayscale: true
    blur: false
    threshold: true
  process_every_n_frames: 20

# Logging Configuration
logging:
  level: "INFO"
  format: "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"
  file_path: ""
  max_file_size_mb: 0
  backup_count: 0

# Job Tracking Configuration
job_tracking:
  enabled: true
  retention_days: 7
  cleanup_interval_hours: 24
  history_retention_days: 30

# File Processing Configuration
file_processing:
  # Directory where downloaded videos are temporarily stored
  temp_directory: "/Users/<USER>/Development/upwork/ezlo/_repos/cloudml-v3/savedvideofiles"
  # Directory where videos are permanently stored (if needed)
  video_storage_directory: "/Users/<USER>/Development/upwork/ezlo/_repos/cloudml-v3/savedvideofiles"

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  reload: false
  workers: 4

# Worker Configuration
worker:
  concurrency: 4
  prefetch_multiplier: 1
  max_tasks_per_child: 1000
  heartbeat_interval: 30
  status_report_interval: 60

# OpenTelemetry Configuration
opentelemetry:
  enabled: false
  service_name: "object-detection-service"
  collector_endpoint: "http://localhost:4317"
  sampling_rate: 1.0

# File Validation Configuration
file_validation:
  enabled: true
  max_file_size_mb: 1024
  timeout_seconds: 30
  allowed_content_types:
    - "video/mp4"
    - "video/x-m4v"
  allowed_extensions:
    - ".mp4"
    - ".m4v"
  temp_directory: "/tmp/object-detection"

# Monitoring Configuration
monitoring:
  enabled: true
  cache_ttl_seconds: 5
  default_page_size: 50
  max_page_size: 100
  rate_limit_rpm: 60
  authentication_required: false
  worker_heartbeat_timeout: 60