#!/usr/bin/env python3
"""
Celery worker for object detection tasks.
"""

import os
import sys
import logging
from app.celery_app import celery_app
from app.config.settings import config

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def main():
    """Start the Celery worker."""
    logger.info("Starting Celery worker for object detection")
    
    # Get worker configuration
    worker_config = config.get("worker", {})
    
    # Start worker
    celery_app.worker_main([
        "worker",
        "--loglevel=info",
        f"--concurrency={worker_config.get('concurrency', 4)}",
        f"--prefetch-multiplier={worker_config.get('prefetch_multiplier', 1)}",
        f"--max-tasks-per-child={worker_config.get('max_tasks_per_child', 1000)}",
        "--hostname=object-detection-worker@%h",
        "--queues=video_processing"
    ])

if __name__ == "__main__":
    main()