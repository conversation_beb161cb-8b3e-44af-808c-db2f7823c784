# Object Detection Service

A comprehensive object detection service built with Python, FastAPI, Celery, and RabbitMQ that provides video analysis capabilities including object detection, face recognition, license plate recognition, and barcode recognition.

## Features

- **Object Detection**: Uses YOLOv8 for real-time object detection in videos
- **Face Recognition**: Identifies known faces using the face_recognition library
- **License Plate Recognition**: Detects and reads license plates with EasyOCR
- **Barcode Recognition**: Scans QR codes and barcodes with pyzbar
- **Asynchronous Processing**: Uses Celery with RabbitMQ for distributed task processing
- **Image Export**: Exports detected objects to S3-compatible storage
- **OpenTelemetry Integration**: Distributed tracing support
- **JWT Authentication**: Optional token-based authentication
- **Comprehensive Monitoring**: Queue statistics, job history, and worker information
- **Configurable Detection**: Per-request enable/disable and confidence thresholds
- **CPU/GPU Support**: Configurable device selection for object detection

## Architecture

```mermaid
graph TD
    A[Client] -->|POST /detect| B(FastAPI Server)
    B -->|Validate Auth| B
    B -->|Check Job Exists| C[(MongoDB)]
    B -->|Validate MP4| D[File Validation]
    B -->|Generate OT IDs| E[OpenTelemetry]
    B -->|Queue Task| F[(RabbitMQ Broker)]
    G[Celery Worker] -->|Fetch Task| F
    G -->|Download Video| H[Video URL]
    G -->|Validate MP4| H
    G -->|Process with YOLO| I[YOLO Model]
    G -->|Process Faces| J[Face Recognition]
    G -->|Process License Plates| K[License Plate Recognition]
    G -->|Process Barcodes| L[Barcode Recognition]
    G -->|Export Images| M[S3 Storage]
    G -->|POST Results| N[Callback URL]
    G -->|Cleanup Files| O[File Cleanup]
    P[Docker Container] -->|Contains| B
    P -->|Contains| G
    Q[OTLP Collector] -->|Receive Traces| E
```

## Prerequisites

- Python 3.11
- Docker and Docker Compose (for containerized deployment)
- RabbitMQ (included in docker-compose)
- MongoDB (included in docker-compose)
- S3-compatible storage (for image export feature)

## Quick Start

### Using Setup Scripts

For Unix/Linux/macOS:
```bash
./setup.sh
```

For Windows:
```cmd
setup.bat
```

### Manual Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd object-detection-service
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the application by editing `config.yaml`.

## Configuration

The application can be configured through:
1. `config.yaml` file
2. Default values

Key configuration sections:
- RabbitMQ connection settings
- MongoDB connection settings
- Authentication settings
- YOLO object detection settings
- Face recognition settings
- License plate recognition settings
- Barcode recognition settings
- Image export settings (S3)
- Logging settings
- Job tracking settings
- API settings
- Worker settings
- OpenTelemetry settings

## Usage

### Starting the Services

Using Docker Compose (recommended):
```bash
docker-compose up -d
```

This will start:
- RabbitMQ message broker
- MongoDB database
- FastAPI web service
- Celery worker

### Local Development

See [HOW_TO_RUN.md](HOW_TO_RUN.md) for detailed instructions on running the service locally.

### API Endpoints

- `POST /detect` - Submit a video for detection
- `GET /stats/queue` - Get queue statistics
- `GET /stats/queue/list` - Get list of queued jobs
- `GET /stats/completed` - Get number of completed jobs
- `GET /stats/failed` - Get list of failed jobs
- `GET /stats/processing` - Get number of processing jobs
- `GET /stats/workers` - Get worker information
- `GET /stats/job/{uuid}` - Get job history
- `GET /health` - Health check endpoint

### Detection Request

```json
{
  "uuid": "unique-job-identifier",
  "video_url": "https://example.com/video.mp4",
  "callback_url": "https://your-service.com/callback",
  "object_detection_enabled": true,
  "object_detection_confidence_threshold": 0.5,
  "face_recognition_enabled": true,
  "face_recognition_confidence_threshold": 0.6,
  "license_plate_recognition_enabled": true,
  "license_plate_recognition_confidence_threshold": 0.5,
  "barcode_recognition_enabled": true,
  "barcode_recognition_confidence_threshold": 0.8,
  "detected_image_export": false
}
```

### Response Format

On success, the callback URL will receive:
```json
{
  "uuid": "unique-job-identifier",
  "status": "success",
  "results": {
    "uuid": "unique-job-identifier",
    "timestamp": "2023-05-15T14:30:22Z",
    "video_url": "https://example.com/video.mp4",
    "ot_traceid": "4bf92f98e5ed2d16c1245884b5e07383",
    "ot_spanid": "1234567890abcdef",
    "objects": [...],
    "faces": [...],
    "license_plates": [...],
    "barcodes": [...]
  },
  "error": null,
  "ot_traceid": "4bf92f98e5ed2d16c1245884b5e07383",
  "ot_spanid": "1234567890abcdef"
}
```

On failure, the callback URL will receive:
```json
{
  "uuid": "unique-job-identifier",
  "status": "failed",
  "results": null,
  "error": "Error message",
  "ot_traceid": "4bf92f98e5ed2d16c1245884b5e07383",
  "ot_spanid": "1234567890abcdef"
}
```

## Development

### Project Structure

```
object-detection-service/
├── app/
│   ├── main.py              # FastAPI application
│   ├── models.py            # Data models
│   ├── celery_app.py        # Celery configuration
│   ├── tasks.py             # Celery tasks
│   ├── detection.py         # YOLO object detection
│   ├── face_recognition.py  # Face recognition
│   ├── license_plate.py     # License plate recognition
│   ├── barcode.py           # Barcode recognition
│   ├── image_export.py      # Image export to S3
│   ├── aggregation.py       # Result aggregation
│   ├── job_tracking.py      # Job tracking with MongoDB
│   └── config/
│       └── settings.py      # Configuration management
├── worker/
│   └── worker.py            # Celery worker
├── config.yaml              # Configuration file
├── requirements.txt         # Python dependencies
├── Dockerfile               # Docker configuration
├── docker-compose.yml       # Multi-container setup
└── README.md                # This file
```

### Running Tests

```bash
# Run unit tests
python -m pytest tests/

# Run integration tests
python -m pytest tests/integration/
```

## Deployment


### Scaling

To scale the service:
1. Increase the number of FastAPI instances behind a load balancer
2. Increase the number of Celery workers
3. Use RabbitMQ clustering for high availability
4. Configure GPU resources for workers that need them

## Monitoring

The service provides several monitoring endpoints:
- Queue depth and job status
- Worker performance metrics
- Processing time statistics
- Error rates and failure analysis

## Security

- JWT-based authentication (configurable)
- Input validation for all endpoints
- Secure handling of temporary files
- Protected monitoring endpoints
- Rate limiting for API endpoints

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request