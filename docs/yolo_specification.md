# YOLO Object Detection Specification

## Overview

The application uses <PERSON><PERSON><PERSON> (You Only Look Once) for real-time object detection in video content. The implementation supports both CPU and GPU processing with configurable device selection.

## YOLO Versions

### YOLOv8
- Latest version with improved accuracy and performance
- Supports multiple model sizes (n, s, m, l, x)
- Automatic device selection (CPU/GPU)
- Built-in preprocessing and postprocessing

### Model Selection
- `yolov8n.pt`: Nano model (fastest, least accurate)
- `yolov8s.pt`: Small model (balanced)
- `yolov8m.pt`: Medium model (good accuracy, moderate speed)
- `yolov8l.pt`: Large model (high accuracy, slower)
- `yolov8x.pt`: Extra large model (highest accuracy, slowest)

## Device Configuration

### CPU Processing
- Uses all available CPU cores
- Slower but more compatible
- Lower memory requirements
- Suitable for lightweight deployments

### GPU Processing
- Uses CUDA-enabled NVIDIA GPUs
- Significantly faster processing
- Higher memory requirements
- Requires compatible GPU drivers

### Device Selection
- Configured via `yolo.device` setting ("cpu" or "cuda")
- Automatically falls back to CPU if GPU is unavailable
- Per-process device selection for multi-GPU systems

## Performance Considerations

### Processing Speed
- GPU: 10-50x faster than CPU depending on model size
- CPU: 1-5 FPS for full HD video
- GPU: 10-50 FPS for full HD video

### Memory Usage
- CPU: 2-8 GB RAM depending on model size
- GPU: 4-16 GB VRAM depending on model size
- Additional memory for video buffering

### Model Loading
- Models cached in memory for reuse
- Initial loading time: 5-30 seconds
- Automatic model download if not present

## Configuration Options

```yaml
yolo:
  enabled: true
  model: "yolov8n.pt"
  detection_classes:
    - "person"
    - "car"
    - "truck"
    - "bus"
    - "motorcycle"
    - "bicycle"
  confidence_threshold: 0.5
  nms_threshold: 0.4
  process_every_n_frames: 5
  device: "cpu"  # or "cuda"
```

## Detection Process

### 1. Model Loading
- Load specified YOLO model
- Configure device (CPU/GPU)
- Validate model compatibility

### 2. Video Processing
- Extract frames from video
- Preprocess frames for YOLO input
- Run inference on each frame
- Postprocess results (NMS, filtering)

### 3. Result Formatting
- Convert bounding boxes to relative coordinates
- Filter by confidence threshold
- Filter by specified classes
- Add timestamp and frame information

## Error Handling

### Common Errors
1. **GPU memory overflow**: Reduce batch size or use smaller model
2. **CUDA not available**: Fall back to CPU processing
3. **Model not found**: Download from official repository
4. **Invalid model file**: Re-download or use default model

### Fallback Mechanisms
- Automatic CPU fallback on GPU errors
- Model size reduction on memory errors
- Frame skipping on performance issues

## Integration Points

### Task Processing
- Device configuration passed from task parameters
- Model loaded once per worker process
- Results passed to aggregation module

### Performance Monitoring
- Device usage statistics
- Processing time per frame
- Memory utilization tracking

## Testing

### Unit Tests
- Model loading with different devices
- Inference accuracy validation
- Performance benchmarking
- Error handling scenarios

### Integration Tests
- End-to-end object detection workflow
- CPU/GPU switching
- Model size performance comparison

### Performance Tests
- FPS measurement on different hardware
- Memory usage profiling
- Batch processing efficiency