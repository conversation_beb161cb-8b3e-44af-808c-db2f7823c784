# File Validation Specification

## Overview

The application validates that incoming video URLs point to valid MP4 files before processing. This validation occurs at two points:
1. When the request is received by the FastAPI endpoint
2. When the file is downloaded by the Celery worker

## Validation Methods

### 1. URL-based Validation
- Check file extension in URL (`.mp4`)
- Send HTTP HEAD request to check Content-Type header
- Expected Content-Type: `video/mp4`

### 2. Content-based Validation
- Download first few bytes of file (magic number)
- Check MP4 signature in file header
- Validate file structure

## MP4 File Format Validation

### Magic Number Check
- First 4 bytes should be compatible with MP4 format
- Common signatures:
  - `0x00000018` (size of first atom)
  - `0x66747970` (file type atom "ftyp")
  - `0x6D703432` (MP4 version 2)

### File Structure Validation
- Check for required atoms:
  - `ftyp`: File type
  - `moov`: Movie metadata
  - `mdat`: Media data

## Validation Process

### FastAPI Endpoint Validation
1. Check URL extension
2. Send HEAD request to verify Content-Type
3. If validation fails, return 400 Bad Request

### Worker Validation
1. Download file to temporary location
2. Check file magic number
3. Validate MP4 structure
4. If validation fails, mark task as failed and send error callback

## Error Handling

### Validation Failures
- Return appropriate HTTP status codes:
  - 400 Bad Request for invalid file format
  - 415 Unsupported Media Type for non-MP4 files

### Error Response Format
```json
{
  "status": "error",
  "message": "Invalid file format. Only MP4 files are supported.",
  "details": {
    "url": "https://example.com/video.mp4",
    "content_type": "video/avi",
    "file_extension": ".avi"
  }
}
```

## Temporary File Management

### Storage Location
- Use system temporary directory
- Create unique subdirectory for each job
- Path format: `/tmp/object-detection/{uuid}/`

### File Naming
- Downloaded file: `video.mp4`
- Temporary directory automatically cleaned up after processing

### Cleanup Process
- Delete temporary directory and all contents after processing
- Cleanup on both success and failure
- Error handling for cleanup failures

## Performance Considerations

### HEAD Request Optimization
- Use connection pooling for HTTP requests
- Set appropriate timeouts (default: 10 seconds)
- Cache validation results for identical URLs

### Partial Download for Validation
- Download only first 1KB for magic number check
- Avoid full file download for validation
- Stream validation during download

## Security Considerations

### File Size Limits
- Maximum file size: 1GB (configurable)
- Return 413 Payload Too Large for oversized files
- Monitor memory usage during validation

### Path Traversal Prevention
- Sanitize file names
- Use secure temporary directory creation
- Validate file paths before access

### Malicious File Detection
- Check for known malicious signatures
- Limit validation time (default: 30 seconds)
- Abort validation on suspicious patterns

## Configuration Options

```yaml
file_validation:
  enabled: true
  max_file_size_mb: 1024
  timeout_seconds: 30
  allowed_content_types:
    - "video/mp4"
    - "video/x-m4v"
  allowed_extensions:
    - ".mp4"
    - ".m4v"
  temp_directory: "/tmp/object-detection"
```

## Logging

### Validation Events
- Log validation start and completion
- Log validation failures with details
- Include job UUID in all validation logs

### Log Format
```
[timestamp] [level] [module] [task_id] [uuid] [message]
```

Example:
```
[2023-05-15 14:30:22,123] [INFO] [file_utils] [task_12345] [uuid-abc-123] File validation passed for https://example.com/video.mp4