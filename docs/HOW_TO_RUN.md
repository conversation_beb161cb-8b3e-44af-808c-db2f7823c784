# How to Run the Object Detection Service Locally

This document provides step-by-step instructions to run the object detection service on your local machine.

## Prerequisites

Ensure you have the following installed:
- Python 3.11
- Git
- Docker (for running dependencies)

## Step-by-Step Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd object-detection-service
```

### 2. Set Up Virtual Environment

```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
# Install Python packages
pip install -r requirements.txt
```


### 5. Start Required Services with Docker

```bash
# Start RabbitMQ and MongoDB
docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management
docker run -d --name mongodb -p 27017:27017 -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=password mongo:5.0
```

### 6. Run the Application

In the first terminal:
```bash
# Activate virtual environment
source venv/bin/activate

# Start FastAPI application
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

In the second terminal:
```bash
# Activate virtual environment
source venv/bin/activate

# Start Celery worker
celery -A app.celery_app worker --loglevel=info --queues=video_processing
```

### 7. Test the Application

Check if the service is running:
```bash
curl http://localhost:8000/health
```

Submit a test job:
```bash
curl -X POST http://localhost:8000/detect \
  -H "Content-Type: application/json" \
  -d '{
    "uuid": "test-job-001",
    "video_url": "https://example.com/sample-video.mp4",
    "callback_url": "https://your-callback-endpoint.com/results",
    "object_detection_enabled": true,
    "face_recognition_enabled": true,
    "license_plate_recognition_enabled": true,
    "barcode_recognition_enabled": true,
    "detected_image_export": false
  }'
```

### 8. View Logs

```bash
# View application logs
tail -f logs/app.log
```

## Stopping the Services

1. Press `Ctrl+C` in the terminals running the FastAPI app and Celery worker
2. Stop Docker containers:
   ```bash
   docker stop rabbitmq mongodb
   docker rm rabbitmq mongodb
   ```
3. Deactivate virtual environment:
   ```bash
   deactivate
   ```

## Troubleshooting

If you encounter issues:

1. Check that all required services are running
2. Verify environment variables are set correctly
3. Check logs for error messages
4. Ensure you're using Python 3.11