#!/usr/bin/env python3
"""
Test script to verify video processing functionality.
"""

import os
import sys
import tempfile
import cv2
import numpy as np
from app.detection import detect_objects
from app.face_recognition import recognize_faces
from app.license_plate import recognize_license_plates
from app.barcode import recognize_barcodes

def create_test_video(video_path: str, duration: int = 10):
    """
    Create a simple test video with some shapes for testing.
    
    Args:
        video_path: Path to save the video
        duration: Duration of the video in seconds
    """
    print(f"Creating test video at {video_path}")
    
    # Video properties
    width, height = 640, 480
    fps = 30
    total_frames = duration * fps
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
    
    # Create frames with different shapes
    for i in range(total_frames):
        # Create a black image
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add some shapes
        if i < total_frames // 4:
            # Draw a rectangle (could be detected as a "car")
            cv2.rectangle(frame, (100, 100), (300, 200), (0, 255, 0), -1)
        elif i < total_frames // 2:
            # Draw a circle (could be detected as a "person")
            cv2.circle(frame, (320, 240), 50, (255, 0, 0), -1)
        elif i < 3 * total_frames // 4:
            # Draw a triangle (could be detected as a "traffic light")
            points = np.array([[400, 100], [500, 200], [300, 200]], np.int32)
            cv2.fillPoly(frame, [points], (0, 0, 255))
        else:
            # Draw text (for barcode testing)
            cv2.putText(frame, "TEST123", (200, 240), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # Write the frame
        out.write(frame)
    
    # Release everything
    out.release()
    print(f"Test video created with {total_frames} frames")

def test_object_detection(video_path: str):
    """Test object detection functionality."""
    print("\nTesting object detection...")
    try:
        # For testing purposes, we'll use a very low confidence threshold
        # since our test video doesn't contain real objects
        objects = detect_objects(
            video_path,
            detection_classes=["person", "car", "traffic light"],
            confidence_threshold=0.1,  # Very low threshold for testing
            device="cpu"
        )
        print(f"✓ Object detection completed. Found {len(objects)} objects")
        return True
    except Exception as e:
        print(f"✗ Object detection failed: {str(e)}")
        return False

def test_face_recognition(video_path: str):
    """Test face recognition functionality."""
    print("\nTesting face recognition...")
    try:
        # For testing purposes, we'll use a very low confidence threshold
        faces = recognize_faces(
            video_path,
            confidence_threshold=0.1  # Very low threshold for testing
        )
        print(f"✓ Face recognition completed. Found {len(faces)} faces")
        return True
    except Exception as e:
        print(f"✗ Face recognition failed: {str(e)}")
        return False

def test_license_plate_recognition(video_path: str):
    """Test license plate recognition functionality."""
    print("\nTesting license plate recognition...")
    try:
        # For testing purposes, we'll use a very low confidence threshold
        plates = recognize_license_plates(
            video_path,
            confidence_threshold=0.1  # Very low threshold for testing
        )
        print(f"✓ License plate recognition completed. Found {len(plates)} plates")
        return True
    except Exception as e:
        print(f"✗ License plate recognition failed: {str(e)}")
        return False

def test_barcode_recognition(video_path: str):
    """Test barcode recognition functionality."""
    print("\nTesting barcode recognition...")
    try:
        barcodes = recognize_barcodes(
            video_path,
            confidence_threshold=0.1  # Very low threshold for testing
        )
        print(f"✓ Barcode recognition completed. Found {len(barcodes)} barcodes")
        return True
    except Exception as e:
        print(f"✗ Barcode recognition failed: {str(e)}")
        return False

def main():
    """Run all video processing tests."""
    print("Object Detection Service Video Processing Test")
    print("=" * 50)
    
    # Create temporary directory for test files
    temp_dir = tempfile.mkdtemp(prefix="object_detection_test_")
    video_path = os.path.join(temp_dir, "test_video.mp4")
    
    try:
        # Create test video
        create_test_video(video_path, duration=5)
        
        # Run tests
        tests = [
            test_object_detection,
            test_face_recognition,
            test_license_plate_recognition,
            test_barcode_recognition
        ]
        
        all_passed = True
        for test in tests:
            if not test(video_path):
                all_passed = False
        
        print("\n" + "=" * 50)
        if all_passed:
            print("✓ All video processing tests completed!")
        else:
            print("✗ Some video processing tests failed!")
        
        return all_passed
        
    finally:
        # Clean up temporary files
        try:
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                print(f"\nCleaned up temporary directory: {temp_dir}")
        except Exception as e:
            print(f"\nFailed to clean up temporary directory: {str(e)}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)