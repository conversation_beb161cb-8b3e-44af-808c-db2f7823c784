# Workflow Diagram

## Complete Process Flow

```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant RabbitMQ
    participant Worker
    participant YOLO
    participant Callback

    Client->>FastAPI: POST /detect {uuid, video_url, callback_url}
    FastAPI->>RabbitMQ: Queue task with parameters
    FastAPI-->>Client: Response {status: queued, task_id}
    
    loop Every few seconds
        Worker->>RabbitMQ: Check for tasks
        RabbitMQ-->>Worker: Return task if available
    end
    
    Worker->>Worker: Download video from URL
    Worker->>YOLO: Process video frames
    YOLO-->>Worker: Return detection results
    Worker->>Callback: POST results to callback_url
```

## Error Handling Flow

```mermaid
graph TD
    A[Task Processing] --> B{Error Occurred?}
    B -->|Yes| C[Log Error]
    C --> D{Retry Possible?}
    D -->|Yes| E[Schedule Retry]
    D -->|No| F[Mark as Failed]
    F --> G[Send Error to Callback]
    B -->|No| H[Send Success to Callback]
```

## Retry Logic

1. **Video Download Failures**:
   - Retry up to 3 times
   - Exponential backoff (1s, 2s, 4s)
   - If all retries fail, mark task as failed

2. **Callback Failures**:
   - Retry up to 3 times
   - Exponential backoff (1s, 2s, 4s)
   - If all retries fail, log error and mark task as failed

3. **YOLO Processing Failures**:
   - No retries, mark as failed immediately
   - Log detailed error information