#!/usr/bin/env python3
"""
Test script to verify the setup of the object detection service.
"""

import os
import sys
import subprocess
import time
import requests
from app.config.settings import config

def check_python_version():
    """Check if Python 3.11 is installed."""
    print("Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} is installed")
        return True
    else:
        print(f"✗ Python 3.11 or higher is required. Found {version.major}.{version.minor}.{version.micro}")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    print("\nChecking dependencies...")
    required_packages = [
        "fastapi",
        "uvicorn",
        "celery",
        "pika",
        "ultralytics",
        "face_recognition",
        "easyocr",
        "pyzbar",
        "requests",
        "pyjwt",
        "pyyaml",
        "opentelemetry-api",
        "boto3",
        "Pillow",
        "python-magic"
    ]
    
    try:
        import pkg_resources
        installed_packages = [d.project_name for d in pkg_resources.working_set]
        
        missing_packages = []
        for package in required_packages:
            if package not in installed_packages:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"✗ Missing packages: {missing_packages}")
            return False
        else:
            print("✓ All required packages are installed")
            return True
    except Exception as e:
        print(f"✗ Error checking dependencies: {str(e)}")
        return False

def check_docker():
    """Check if Docker is installed and running."""
    print("\nChecking Docker...")
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Docker is installed: {result.stdout.strip()}")
            
            # Check if Docker daemon is running
            result = subprocess.run(["docker", "info"], capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ Docker daemon is running")
                return True
            else:
                print("✗ Docker daemon is not running")
                return False
        else:
            print("✗ Docker is not installed")
            return False
    except FileNotFoundError:
        print("✗ Docker is not installed")
        return False

def check_docker_compose():
    """Check if Docker Compose is installed."""
    print("\nChecking Docker Compose...")
    try:
        result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Docker Compose is installed: {result.stdout.strip()}")
            return True
        else:
            print("✗ Docker Compose is not installed")
            return False
    except FileNotFoundError:
        print("✗ Docker Compose is not installed")
        return False

def check_config_file():
    """Check if config file exists and is valid."""
    print("\nChecking configuration file...")
    config_path = "config.yaml"
    if os.path.exists(config_path):
        print("✓ Configuration file exists")
        try:
            # Try to access a config value
            rabbitmq_host = config.get("rabbitmq.host")
            print(f"✓ Configuration file is valid. RabbitMQ host: {rabbitmq_host}")
            return True
        except Exception as e:
            print(f"✗ Configuration file is invalid: {str(e)}")
            return False
    else:
        print("✗ Configuration file does not exist")
        return False

def check_required_directories():
    """Check if required directories exist."""
    print("\nChecking required directories...")
    required_dirs = ["logs", "known_faces"]
    missing_dirs = []
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✓ {directory} directory exists")
        else:
            print(f"✗ {directory} directory does not exist")
            missing_dirs.append(directory)
    
    return len(missing_dirs) == 0

def main():
    """Run all setup checks."""
    print("Object Detection Service Setup Verification")
    print("=" * 50)
    
    checks = [
        check_python_version,
        check_dependencies,
        check_docker,
        check_docker_compose,
        check_config_file,
        check_required_directories
    ]
    
    all_passed = True
    for check in checks:
        if not check():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ All setup checks passed!")
        print("\nYou can now start the service with:")
        print("  docker-compose up -d")
    else:
        print("✗ Some setup checks failed!")
        print("\nPlease fix the issues above before starting the service.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)