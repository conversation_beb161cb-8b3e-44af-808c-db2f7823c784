# OpenTelemetry Specification

## Overview

The application integrates with OpenTelemetry for distributed tracing to provide visibility into request flows and performance monitoring.

## Trace Context Propagation

### Incoming Requests
- Extract `ot_traceid` and `ot_spanid` from request headers
- If not present, generate new trace and span IDs
- Create a new span for the incoming request
- Propagate trace context to Celery tasks

### Outgoing Requests
- Propagate trace context to:
  - Celery task queuing
  - Callback URL requests
  - External service calls

## Trace Structure

### Root Span (FastAPI Endpoint)
- Name: `POST /detect`
- Attributes:
  - `http.method`: "POST"
  - `http.route`: "/detect"
  - `http.status_code`: Response status code
  - `job.uuid`: Job UUID
  - `job.video_url`: Video URL (sanitized)
  - `job.callback_url`: Callback URL (sanitized)

### Celery Task Span
- Name: `process_video_task`
- Attributes:
  - `job.uuid`: Job UUID
  - `job.video_url`: Video URL (sanitized)
  - `messaging.system`: "rabbitmq"
  - `messaging.destination`: Queue name

### Sub-spans
1. **Video Download**
   - Name: `download_video`
   - Attributes:
     - `video.url`: Video URL (sanitized)
     - `file.size`: Size of downloaded file

2. **MP4 Validation**
   - Name: `validate_mp4`
   - Attributes:
     - `file.valid`: Boolean result

3. **Object Detection**
   - Name: `yolo_detection`
   - Attributes:
     - `model.name`: YOLO model name
     - `frames.processed`: Number of frames processed
     - `objects.detected`: Number of objects detected

4. **Callback Request**
   - Name: `send_callback`
   - Attributes:
     - `http.url`: Callback URL (sanitized)
     - `http.status_code`: Response status code

## Trace ID and Span ID Generation

### Format
- Trace ID: 32-character lowercase hexadecimal string
- Span ID: 16-character lowercase hexadecimal string

### Generation
- Use cryptographically secure random number generation
- Follow W3C Trace Context standards

## Context Propagation

### HTTP Headers
- `traceparent`: W3C Trace Context header
- `tracestate`: W3C Trace State header

### Celery Integration
- Use Celery's built-in OpenTelemetry integration
- Propagate trace context through task metadata

## Export Configuration

### Exporter Options
1. **Console Exporter** (Development)
   - Output traces to console
   - Human-readable format

2. **OTLP Exporter** (Production)
   - Export to OpenTelemetry Collector
   - gRPC or HTTP protocol
   - Configurable endpoint

### Environment Configuration
- `OTEL_EXPORTER_OTLP_ENDPOINT`: OTLP collector endpoint
- `OTEL_SERVICE_NAME`: Service name for traces
- `OTEL_TRACES_EXPORTER`: Exporter type ("otlp", "console", or "none")

## Sampling

### Default Sampling
- Always sample traces for detection requests
- 10% sampling for statistical endpoints

### Configurable Sampling
- `otel.tracing.sampling_rate`: Float between 0.0 and 1.0
- 1.0 = 100% sampling, 0.0 = 0% sampling

## Metrics (Future Enhancement)

### Planned Metrics
1. **Request Rate**
   - Counter: `detection_requests_total`
   - Labels: status, auth_enabled

2. **Processing Duration**
   - Histogram: `processing_duration_seconds`
   - Labels: status, video_duration

3. **Queue Depth**
   - Gauge: `queue_depth`
   - Labels: status

4. **Error Rates**
   - Counter: `errors_total`
   - Labels: error_type, component

## Integration Points

### FastAPI Middleware
- Automatic instrumentation of HTTP requests
- Trace context extraction from headers
- Span creation for each request

### Celery Instrumentation
- Task execution tracing
- Queue time measurement
- Worker performance metrics

### Custom Spans
- Manual span creation for key operations
- Error reporting in spans
- Attribute enrichment

## Configuration

```yaml
opentelemetry:
  enabled: true
  service_name: "object-detection-service"
  collector_endpoint: "http://localhost:4317"
  sampling_rate: 1.0
  exporters:
    - "otlp"
    - "console"
```

## Error Handling

### Trace Errors
- Record exceptions in spans
- Set span status to ERROR
- Include error details as attributes

### Export Errors
- Log export failures
- Continue application operation despite export errors
- Alert on persistent export issues