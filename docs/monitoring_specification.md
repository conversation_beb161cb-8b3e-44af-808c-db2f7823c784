# Monitoring Specification

## Overview

The application provides built-in monitoring endpoints to track the status of the job queue, completed jobs, failed jobs, worker information, and detailed job history. These endpoints help operators understand the system's current state and performance.

## Monitoring Endpoints

### 1. Queue Statistics
- **Endpoint**: `GET /stats/queue`
- **Response**:
  ```json
  {
    "count": 42,
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns the number of jobs currently in the queue

### 2. Queue List
- **Endpoint**: `GET /stats/queue/list`
- **Response**:
  ```json
  {
    "jobs": [
      {
        "uuid": "uuid-123",
        "status": "queued",
        "created_at": "2023-05-15T14:25:10Z",
        "video_url": "https://example.com/video1.mp4"
      },
      {
        "uuid": "uuid-124",
        "status": "queued",
        "created_at": "2023-05-15T14:26:15Z",
        "video_url": "https://example.com/video2.mp4"
      }
    ],
    "total": 2,
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns a list of jobs currently in the queue

### 3. Completed Jobs Count
- **Endpoint**: `GET /stats/completed`
- **Response**:
  ```json
  {
    "count": 128,
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns the number of successfully completed jobs

### 4. Failed Jobs List
- **Endpoint**: `GET /stats/failed`
- **Response**:
  ```json
  {
    "jobs": [
      {
        "uuid": "uuid-125",
        "status": "failed",
        "error": "Video download failed",
        "created_at": "2023-05-15T14:20:10Z",
        "failed_at": "2023-05-15T14:21:30Z"
      }
    ],
    "total": 1,
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns a list of failed jobs with error details

### 5. Processing Jobs Count
- **Endpoint**: `GET /stats/processing`
- **Response**:
  ```json
  {
    "count": 8,
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns the number of jobs currently being processed

### 6. Worker Information
- **Endpoint**: `GET /stats/workers`
- **Response**:
  ```json
  {
    "workers": [
      {
        "id": "worker-1",
        "hostname": "worker-host-1",
        "pid": 12345,
        "active_tasks": 2,
        "processed_tasks": 42,
        "status": "online",
        "last_heartbeat": "2023-05-15T14:30:22Z"
      },
      {
        "id": "worker-2",
        "hostname": "worker-host-2",
        "pid": 12346,
        "active_tasks": 1,
        "processed_tasks": 38,
        "status": "online",
        "last_heartbeat": "2023-05-15T14:30:20Z"
      }
    ],
    "total": 2,
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns information about active workers

### 7. Job History
- **Endpoint**: `GET /stats/job/{uuid}`
- **Response**:
  ```json
  {
    "job": {
      "uuid": "uuid-123",
      "status": "completed",
      "created_at": "2023-05-15T14:25:10Z",
      "started_at": "2023-05-15T14:25:15Z",
      "completed_at": "2023-05-15T14:28:30Z",
      "steps": [
        {
          "name": "video_download",
          "started_at": "2023-05-15T14:25:15Z",
          "completed_at": "2023-05-15T14:25:25Z",
          "duration_ms": 10000,
          "status": "success",
          "details": {
            "file_size": "1024MB",
            "download_speed": "102.4MB/s"
          }
        },
        {
          "name": "object_detection",
          "started_at": "2023-05-15T14:25:25Z",
          "completed_at": "2023-05-15T14:27:30Z",
          "duration_ms": 125000,
          "status": "success",
          "details": {
            "frames_processed": 1500,
            "objects_detected": 42
          }
        },
        {
          "name": "face_recognition",
          "started_at": "2023-05-15T14:27:30Z",
          "completed_at": "2023-05-15T14:28:15Z",
          "duration_ms": 45000,
          "status": "success",
          "details": {
            "faces_detected": 8,
            "faces_recognized": 5
          }
        },
        {
          "name": "license_plate_recognition",
          "started_at": "2023-05-15T14:28:15Z",
          "completed_at": "2023-05-15T14:28:25Z",
          "duration_ms": 10000,
          "status": "success",
          "details": {
            "plates_detected": 3,
            "plates_recognized": 2
          }
        },
        {
          "name": "barcode_recognition",
          "started_at": "2023-05-15T14:28:25Z",
          "completed_at": "2023-05-15T14:28:28Z",
          "duration_ms": 3000,
          "status": "success",
          "details": {
            "barcodes_detected": 1,
            "barcodes_recognized": 1
          }
        },
        {
          "name": "result_posting",
          "started_at": "2023-05-15T14:28:28Z",
          "completed_at": "2023-05-15T14:28:30Z",
          "duration_ms": 2000,
          "status": "success",
          "details": {
            "response_status": 200
          }
        }
      ],
      "response_data": "{\"uuid\":\"uuid-123\",\"status\":\"success\",\"results\":{\"objects\":[...],\"faces\":[...],\"license_plates\":[...],\"barcodes\":[...]}}",
      "error_message": null
    },
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns detailed history for a specific job including timing information for each step

### 8. System Health
- **Endpoint**: `GET /health`
- **Response**:
  ```json
  {
    "status": "healthy",
    "components": {
      "rabbitmq": "healthy",
      "job_tracker": "healthy",
      "file_system": "healthy"
    },
    "timestamp": "2023-05-15T14:30:22Z"
  }
  ```
- **Description**: Returns overall system health status

## Data Sources

### Job Tracker Integration
- All statistics are derived from the job tracking system
- Real-time data with minimal performance impact
- Consistent data across multiple application instances

### RabbitMQ Integration
- Queue depth information from RabbitMQ management API
- Worker status and processing rates
- Connection and channel statistics

### Worker Status Reporting
- Workers periodically report their status
- Heartbeat mechanism to detect offline workers
- Task processing metrics

## Performance Considerations

### Caching
- Cache statistical data for 5 seconds to reduce load
- Separate cache for each endpoint
- Cache invalidation on job status changes

### Pagination
- Queue list and failed jobs list support pagination
- Default page size: 50 items
- Maximum page size: 100 items

### Filtering and Sorting
- Filter by date range
- Filter by job status
- Sort by creation time or update time

## Security

### Authentication
- Monitoring endpoints can be protected with JWT authentication
- Separate permission for monitoring access
- Read-only access to prevent system modifications

### Rate Limiting
- Limit requests to 60 per minute per IP
- Separate rate limit for monitoring endpoints
- Exponential backoff for excessive requests

## Response Format

### Success Response
```json
{
  "data": {...},
  "timestamp": "2023-05-15T14:30:22Z",
  "request_id": "req-12345"
}
```

### Error Response
```json
{
  "error": {
    "code": "STATS_UNAVAILABLE",
    "message": "Unable to retrieve statistics",
    "details": "RabbitMQ connection failed"
  },
  "timestamp": "2023-05-15T14:30:22Z",
  "request_id": "req-12345"
}
```

## Configuration Options

```yaml
monitoring:
  enabled: true
  cache_ttl_seconds: 5
  default_page_size: 50
  max_page_size: 100
  rate_limit_rpm: 60
  authentication_required: false
  worker_heartbeat_timeout: 60
  job_history_retention_days: 30
```

## Integration with External Monitoring

### Prometheus Metrics (Future Enhancement)
- Export metrics in Prometheus format
- Endpoint: `/metrics`
- Standard metrics for queue depth, processing time, error rates

### Log Aggregation
- Structured logging for monitoring events
- Include request IDs for traceability
- Log level configuration for monitoring events

## Alerting

### Threshold-based Alerts
- Queue depth exceeds threshold
- Failed job rate exceeds threshold
- Processing time exceeds threshold
- Worker offline detection

### Health Check Alerts
- System component failures
- Performance degradation
- Resource exhaustion

## API Documentation

### OpenAPI Specification
- Automatic generation of API documentation
- Interactive API explorer
- Schema validation for requests and responses

### Example Usage
```bash
# Get queue statistics
curl -X GET http://localhost:8000/stats/queue

# Get list of failed jobs
curl -X GET http://localhost:8000/stats/failed

# Get worker information
curl -X GET http://localhost:8000/stats/workers

# Get job history
curl -X GET http://localhost:8000/stats/job/uuid-123

# Get system health
curl -X GET http://localhost:8000/health