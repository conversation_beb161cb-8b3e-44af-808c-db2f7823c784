# Face Recognition Specification

## Overview

The application integrates face recognition capabilities using the `face-recognition` library to identify known faces in video content alongside object detection.

## Library Selection

### face-recognition
- Python library for face recognition
- Built on dlib's state-of-the-art face recognition framework
- Pre-trained models for face detection and recognition
- Simple API for face comparison and identification
- Good performance for real-world applications

## Face Recognition Process

### 1. Face Detection
- Locate faces in each video frame
- Use HOG-based face detector (default) or CNN-based detector (more accurate but slower)
- Return bounding boxes for detected faces

### 2. Face Encoding
- Generate 128-dimensional face encodings for each detected face
- Use pre-trained deep learning model
- Encodings represent facial features mathematically

### 3. Face Comparison
- Compare face encodings with known face encodings
- Calculate Euclidean distance between encodings
- Threshold-based classification (default threshold: 0.6)

### 4. Face Identification
- Match detected faces with known individuals
- Return identity with confidence score
- Label unrecognized faces as "unknown"

## Known Faces Database

### Storage
- JSON file or directory of image files
- Each known person has a folder with reference images
- Automatic encoding generation from reference images

### Structure
```
known_faces/
├── person1/
│   ├── photo1.jpg
│   ├── photo2.jpg
│   └── photo3.jpg
├── person2/
│   ├── photo1.jpg
│   └── photo2.jpg
└── encodings.json  # Pre-computed encodings
```

### Encoding Generation
- Run offline process to generate encodings from reference images
- Store encodings in JSON format for fast loading
- Update encodings when new reference images are added

## Configuration Options

```yaml
face_recognition:
  enabled: true
  model: "hog"  # or "cnn"
  tolerance: 0.6
  upsample_times: 1
  num_jitters: 1
  known_faces_directory: "known_faces"
  encodings_file: "known_faces/encodings.json"
  detect_faces_only: false  # If true, only detect faces without recognition
```

## Performance Considerations

### Processing Speed
- Face detection is more computationally expensive than object detection
- Process every N frames to balance accuracy and performance
- Default: Process every 5 frames (configurable)

### Memory Usage
- Load known face encodings once at startup
- Cache face encodings in memory for fast access
- Limit concurrent face recognition tasks

### GPU Acceleration
- Optional GPU support for faster processing
- Requires CUDA-enabled system
- Significant performance improvement for CNN model

## Integration with Object Detection

### Parallel Processing
- Run face recognition and object detection concurrently
- Share video frame data to avoid redundant processing
- Combine results in final output

### Result Merging
- Include both object detection and face recognition results
- Maintain separate result lists for clarity
- Preserve frame timestamps for synchronization

## Data Models

### Face Recognition Results
```python
class RecognizedFace:
    name: str  # "unknown" for unrecognized faces
    confidence: float  # Distance score (lower is better)
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float
```

### Combined Results
```python
class DetectionResult:
    uuid: str
    timestamp: str
    objects: Optional[List[DetectedObject]]
    faces: Optional[List[RecognizedFace]]
    video_url: str
```

## Error Handling

### Common Errors
1. **No reference images**: Return error if known_faces directory is empty
2. **Invalid encodings**: Regenerate encodings if JSON file is corrupted
3. **Face detection failure**: Continue processing other frames
4. **Memory issues**: Reduce upsample_times or num_jitters

### Fallback Mechanisms
- Disable face recognition if persistent errors occur
- Log errors and continue with object detection only
- Alert operators on configuration issues

## Training and Setup

### Initial Setup
1. Create known_faces directory
2. Add reference images for each person
3. Run encoding generation script
4. Verify encodings are correctly generated

### Adding New Faces
1. Add new reference images to appropriate folders
2. Run incremental encoding update
3. Validate new encodings

### Updating Encodings
- Full regeneration: Process all reference images
- Incremental update: Process only new/modified images
- Backup existing encodings before updates

## Privacy Considerations

### Data Handling
- Process video content locally without uploading
- Do not store face encodings or images beyond processing
- Clear temporary data after processing

### Consent
- Ensure proper consent for face recognition processing
- Document data handling procedures
- Comply with privacy regulations (GDPR, etc.)

## Testing

### Unit Tests
- Face detection accuracy with test images
- Face recognition accuracy with known individuals
- Performance benchmarks for different configurations

### Integration Tests
- End-to-end face recognition workflow
- Combined object detection and face recognition
- Error handling scenarios

### Performance Tests
- Processing time for various video lengths
- Memory usage under load
- Accuracy with different face angles and lighting