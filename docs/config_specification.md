# Configuration Specification

## Configuration File Structure

The application will use a YAML configuration file named `config.yaml` located in the root directory.

```yaml
# RabbitMQ Configuration
rabbitmq:
  host: "localhost"
  port: 5672
  username: "guest"
  password: "guest"
  vhost: "/"

# PostgreSQL Configuration
postgresql:
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "password"
  database: "object_detection"
  echo: false

# Authentication Configuration
auth:
  enabled: false
  secret_key: "your-secret-key-here"
  algorithm: "HS256"

# YOLO Configuration
yolo:
  enabled: true
  model: "yolov8n.pt"
  detection_classes:
    - "person"
    - "car"
    - "truck"
    - "bus"
    - "motorcycle"
    - "bicycle"
  confidence_threshold: 0.5
  nms_threshold: 0.4
  process_every_n_frames: 5
  device: "cpu"

# Face Recognition Configuration
face_recognition:
  enabled: true
  model: "hog"  # or "cnn"
  tolerance: 0.6
  upsample_times: 1
  num_jitters: 1
  known_faces_directory: "known_faces"
  encodings_file: "known_faces/encodings.json"
  detect_faces_only: false  # If true, only detect faces without recognition
  process_every_n_frames: 10

# License Plate Recognition Configuration
license_plate_recognition:
  enabled: true
  ocr_library: "easyocr"
  languages: ["en"]
  detection_model: "best"
  confidence_threshold: 0.5
  whitelist_characters: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
  preprocess_enabled: true
  preprocess_options:
    grayscale: true
    blur: false
    threshold: true
  process_every_n_frames: 15

# Barcode Recognition Configuration
barcode_recognition:
  enabled: true
  library: "pyzbar"
  formats: 
    - "QR_CODE"
    - "CODE128"
    - "EAN13"
    - "CODE39"
  confidence_threshold: 0.8
  preprocess_enabled: true
  preprocess_options:
    grayscale: true
    blur: false
    threshold: true
  process_every_n_frames: 20

# Image Export Configuration
image_export:
  enabled: false
  s3:
    endpoint_url: "https://s3.amazonaws.com"
    access_key_id: "your-access-key-id"
    secret_access_key: "your-secret-access-key"
    region_name: "us-east-1"
    bucket_name: "your-bucket-name"
    path_prefix: "detections"
  image:
    format: "JPEG"
    quality: 85
    max_width: 800
    max_height: 600
    padding: 20
  url:
    expiration_hours: 24
    public_access: true

# Logging Configuration
logging:
  level: "INFO"
  format: "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"
  file_path: "logs/app.log"
  max_file_size_mb: 10
  backup_count: 5

# Job Tracking Configuration
job_tracking:
  enabled: true
  retention_days: 7
  cleanup_interval_hours: 24
  storage_type: "mongodb"
  mongodb:
    host: "localhost"
    port: 27017
    username: "admin"
    password: "password"
    database: "object_detection"
    collection: "jobs"
  history_retention_days: 30

# File Processing Configuration
file_processing:
  # Directory where downloaded videos are temporarily stored
  temp_directory: "/tmp/object-detection"
  # Directory where videos are permanently stored (if needed)
  video_storage_directory: "/var/lib/object-detection/videos"

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  reload: true
  workers: 1

# Worker Configuration
worker:
  concurrency: 4
  prefetch_multiplier: 1
  max_tasks_per_child: 1000

# OpenTelemetry Configuration
opentelemetry:
  enabled: true
  service_name: "object-detection-service"
  collector_endpoint: "http://localhost:4317"
  sampling_rate: 1.0

# File Validation Configuration
file_validation:
  enabled: true
  max_file_size_mb: 1024
  timeout_seconds: 30
  allowed_content_types:
    - "video/mp4"
    - "video/x-m4v"
  allowed_extensions:
    - ".mp4"
    - ".m4v"
  temp_directory: "/tmp/object-detection"

# Monitoring Configuration
monitoring:
  enabled: true
  cache_ttl_seconds: 5
  default_page_size: 50
  max_page_size: 100
  rate_limit_rpm: 60
  authentication_required: false
```

## Environment Variables

Configuration can also be overridden using environment variables:

- `RABBITMQ_HOST` - RabbitMQ host
- `RABBITMQ_PORT` - RabbitMQ port
- `RABBITMQ_USERNAME` - RabbitMQ username
- `RABBITMQ_PASSWORD` - RabbitMQ password
- `MONGODB_HOST` - MongoDB host
- `MONGODB_PORT` - MongoDB port
- `MONGODB_USERNAME` - MongoDB username
- `MONGODB_PASSWORD` - MongoDB password
- `AUTH_ENABLED` - Enable/disable JWT authentication
- `AUTH_SECRET_KEY` - JWT secret key
- `YOLO_ENABLED` - Enable/disable YOLO object detection
- `YOLO_DEVICE` - Device for YOLO ("cpu" or "cuda")
- `FACE_RECOGNITION_ENABLED` - Enable/disable face recognition
- `LICENSE_PLATE_RECOGNITION_ENABLED` - Enable/disable license plate recognition
- `BARCODE_RECOGNITION_ENABLED` - Enable/disable barcode recognition
- `IMAGE_EXPORT_ENABLED` - Enable/disable image export
- `S3_ENDPOINT_URL` - S3 endpoint URL
- `S3_ACCESS_KEY_ID` - S3 access key ID
- `S3_SECRET_ACCESS_KEY` - S3 secret access key
- `S3_REGION_NAME` - S3 region name
- `S3_BUCKET_NAME` - S3 bucket name
- `LOGGING_LEVEL` - Logging level
- `LOGGING_FILE_PATH` - Log file path
- `YOLO_MODEL` - YOLO model file
- `API_HOST` - API host
- `API_PORT` - API port
- `OTEL_EXPORTER_OTLP_ENDPOINT` - OpenTelemetry collector endpoint

## Configuration Loading Priority

1. Environment variables (highest priority)
2. config.yaml file
3. Default values (lowest priority)

## Default Values

When no configuration is provided, the application will use these defaults:

```python
DEFAULT_CONFIG = {
    "rabbitmq": {
        "host": "localhost",
        "port": 5672,
        "username": "guest",
        "password": "guest",
        "vhost": "/"
    },
    "postgresql": {
        "host": "localhost",
        "port": 5432,
        "username": "postgres",
        "password": "password",
        "database": "object_detection",
        "echo": False
    },
    "auth": {
        "enabled": False,
        "secret_key": "default-secret-key",
        "algorithm": "HS256"
    },
    "yolo": {
        "enabled": True,
        "model": "yolov8n.pt",
        "detection_classes": ["person", "car", "truck"],
        "confidence_threshold": 0.5,
        "nms_threshold": 0.4,
        "process_every_n_frames": 5,
        "device": "cpu"
    },
    "face_recognition": {
        "enabled": True,
        "model": "hog",
        "tolerance": 0.6,
        "upsample_times": 1,
        "num_jitters": 1,
        "known_faces_directory": "known_faces",
        "encodings_file": "known_faces/encodings.json",
        "detect_faces_only": False,
        "process_every_n_frames": 10
    },
    "license_plate_recognition": {
        "enabled": True,
        "ocr_library": "easyocr",
        "languages": ["en"],
        "detection_model": "best",
        "confidence_threshold": 0.5,
        "whitelist_characters": "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "preprocess_enabled": True,
        "preprocess_options": {
            "grayscale": True,
            "blur": False,
            "threshold": True
        },
        "process_every_n_frames": 15
    },
    "barcode_recognition": {
        "enabled": True,
        "library": "pyzbar",
        "formats": ["QR_CODE", "CODE128", "EAN13", "CODE39"],
        "confidence_threshold": 0.8,
        "preprocess_enabled": True,
        "preprocess_options": {
            "grayscale": True,
            "blur": False,
            "threshold": True
        },
        "process_every_n_frames": 20
    },
    "image_export": {
        "enabled": False,
        "s3": {
            "endpoint_url": "https://s3.amazonaws.com",
            "access_key_id": "your-access-key-id",
            "secret_access_key": "your-secret-access-key",
            "region_name": "us-east-1",
            "bucket_name": "your-bucket-name",
            "path_prefix": "detections"
        },
        "image": {
            "format": "JPEG",
            "quality": 85,
            "max_width": 800,
            "max_height": 600,
            "padding": 20
        },
        "url": {
            "expiration_hours": 24,
            "public_access": True
        }
    },
    "logging": {
        "level": "INFO",
        "format": "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s",
        "file_path": "logs/app.log",
        "max_file_size_mb": 10,
        "backup_count": 5
    },
    "job_tracking": {
        "enabled": True,
        "retention_days": 7,
        "cleanup_interval_hours": 24,
        "storage_type": "postgresql",
        "history_retention_days": 30
    },
    "file_processing": {
        "temp_directory": "/tmp/object-detection",
        "video_storage_directory": "/var/lib/object-detection/videos"
    },
    "api": {
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,
        "workers": 1
    },
    "worker": {
        "concurrency": 4,
        "prefetch_multiplier": 1,
        "max_tasks_per_child": 1000
    },
    "opentelemetry": {
        "enabled": True,
        "service_name": "object-detection-service",
        "collector_endpoint": "http://localhost:4317",
        "sampling_rate": 1.0
    },
    "file_validation": {
        "enabled": True,
        "max_file_size_mb": 1024,
        "timeout_seconds": 30,
        "allowed_content_types": ["video/mp4", "video/x-m4v"],
        "allowed_extensions": [".mp4", ".m4v"],
        "temp_directory": "/tmp/object-detection"
    },
    "monitoring": {
        "enabled": True,
        "cache_ttl_seconds": 5,
        "default_page_size": 50,
        "max_page_size": 100,
        "rate_limit_rpm": 60,
        "authentication_required": False
    }
}