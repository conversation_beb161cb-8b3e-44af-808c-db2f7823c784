# Job Tracking Specification

## Overview

The job tracking system prevents duplicate processing of requests with the same UUID. It maintains a record of all jobs that have been queued or are currently being processed.

## Storage Mechanism

### In-Memory Storage (Development)
- Simple dictionary for storing job UUIDs
- Suitable for single-instance deployments
- No persistence across application restarts

### MongoDB Storage (Production)
- MongoDB collection for storing job UUIDs with TTL
- Shared across multiple application instances
- Persistence across application restarts
- Automatic cleanup of expired entries

## Job States

1. **QUEUED** - Job has been added to the task queue
2. **PROCESSING** - Worker has started processing the job
3. **COMPLETED** - Job has been successfully completed
4. **FAILED** - Job processing failed
5. **EXPIRED** - Job record has expired and been removed

## Data Structure

```python
JobRecord = {
    "uuid": str,              # Job UUID
    "state": str,             # Current state
    "task_id": str,           # Celery task ID
    "created_at": datetime,   # When job was created
    "updated_at": datetime,   # When job was last updated
    "expires_at": datetime    # When job record expires
}
```

## API Integration

### Job Existence Check
- Performed before queuing a new task
- Returns 409 Conflict if job already exists
- Response: `{"status": "error", "message": "job already exists"}`

### Job State Updates
- Updated when task is queued: QUEUED
- Updated when worker starts: PROCESSING
- Updated when results are sent: COMPLETED/FAILED

## Expiration and Cleanup

### TTL (Time To Live)
- Default: 7 days
- Configurable via `job_tracking.retention_days`
- Applied to all job records

### Cleanup Process
- Runs periodically (default: every 24 hours)
- Removes expired job records
- Configurable via `job_tracking.cleanup_interval_hours`

## Concurrency Handling

### Race Conditions
- Atomic operations for job creation
- Check-and-set pattern for job state updates
- Distributed locks when using MongoDB storage

### Performance Considerations
- Minimal overhead for job existence checks
- Efficient storage and retrieval
- Background cleanup to prevent performance degradation

## Configuration Options

```yaml
job_tracking:
  enabled: true              # Enable/disable job tracking
  retention_days: 7          # How long to keep job records
  cleanup_interval_hours: 24 # How often to run cleanup
  storage_type: "mongodb"    # "memory" or "mongodb"
  mongodb:
    host: "localhost"
    port: 27017
    username: "admin"
    password: "password"
    database: "object_detection"
    collection: "jobs"
  history_retention_days: 30 # How long to keep detailed history
```

## Error Handling

### Storage Failures
- Log error and continue processing if job tracking fails
- Don't block task queuing due to job tracking issues
- Alert on persistent storage failures

### Consistency Issues
- Log inconsistencies between job tracking and actual task state
- Attempt to reconcile discrepancies
- Manual intervention procedures for severe inconsistencies

## Monitoring and Metrics

### Key Metrics
- Number of active jobs
- Job creation rate
- Duplicate job attempts
- Cleanup effectiveness

### Health Checks
- Job tracking storage connectivity
- Cleanup process status
- Memory usage (for in-memory storage)