# Object Detection Response Example

## Successful Response Example

When a video processing job is successfully completed, the callback URL will receive a POST request with the following JSON payload:

```json
{
  "uuid": "550e8400-e29b-41d4-a716-************",
  "status": "success",
  "results": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "timestamp": "2023-05-15T14:30:22Z",
    "video_url": "https://example.com/video.mp4",
    "ot_traceid": "4bf92f98e5ed2d16c1245884b5e07383",
    "ot_spanid": "1234567890abcdef",
    "objects": [
      {
        "class_name": "person",
        "max_confidence": 0.95,
        "bounding_box": {
          "bounding_box": {
            "x": 120.5,
            "y": 80.2,
            "width": 60.3,
            "height": 120.7
          },
          "frame_number": 45,
          "timestamp": 1.5,
          "confidence": 0.95
        },
        "image_url": "https://object-detection-images.s3.amazonaws.com/detections/550e8400-e29b-41d4-a716-************/objects/1.5_0.95.jpg"
      },
      {
        "class_name": "car",
        "max_confidence": 0.87,
        "bounding_box": {
          "bounding_box": {
            "x": 320.1,
            "y": 240.5,
            "width": 180.3,
            "height": 120.8
          },
          "frame_number": 30,
          "timestamp": 1.0,
          "confidence": 0.87
        },
        "image_url": "https://object-detection-images.s3.amazonaws.com/detections/550e8400-e29b-41d4-a716-************/objects/1.0_0.87.jpg"
      }
    ],
    "faces": [
      {
        "name": "John Doe",
        "max_confidence": 0.92,
        "bounding_box": {
          "bounding_box": {
            "x": 150.2,
            "y": 90.5,
            "width": 45.3,
            "height": 55.7
          },
          "frame_number": 45,
          "timestamp": 1.5,
          "confidence": 0.92
        },
        "image_url": "https://object-detection-images.s3.amazonaws.com/detections/550e8400-e29b-41d4-a716-************/faces/1.5_0.92.jpg"
      },
      {
        "name": "unknown",
        "max_confidence": 0.75,
        "bounding_box": {
          "bounding_box": {
            "x": 280.5,
            "y": 110.2,
            "width": 42.1,
            "height": 52.3
          },
          "frame_number": 60,
          "timestamp": 2.0,
          "confidence": 0.75
        },
        "image_url": "https://object-detection-images.s3.amazonaws.com/detections/550e8400-e29b-41d4-a716-************/faces/2.0_0.75.jpg"
      }
    ],
    "license_plates": [
      {
        "text": "ABC123",
        "max_confidence": 0.95,
        "bounding_box": {
          "bounding_box": {
            "x": 350.2,
            "y": 260.5,
            "width": 120.3,
            "height": 45.7
          },
          "frame_number": 30,
          "timestamp": 1.0,
          "confidence": 0.95
        },
        "image_url": "https://object-detection-images.s3.amazonaws.com/detections/550e8400-e29b-41d4-a716-************/license_plates/1.0_0.95.jpg"
      }
    ],
    "barcodes": [
      {
        "text": "https://example.com",
        "barcode_type": "QR_CODE",
        "max_confidence": 0.99,
        "bounding_box": {
          "bounding_box": {
            "x": 200.5,
            "y": 150.2,
            "width": 80.3,
            "height": 80.7
          },
          "frame_number": 75,
          "timestamp": 2.5,
          "confidence": 0.99
        },
        "image_url": "https://object-detection-images.s3.amazonaws.com/detections/550e8400-e29b-41d4-a716-************/barcodes/2.5_0.99.jpg"
      }
    ]
  },
  "error": null,
  "ot_traceid": "4bf92f98e5ed2d16c1245884b5e07383",
  "ot_spanid": "1234567890abcdef"
}
```

## Error Response Example

If the video processing job fails, the callback URL will receive a POST request with the following JSON payload:

```json
{
  "uuid": "550e8400-e29b-41d4-a716-************",
  "status": "failed",
  "results": null,
  "error": "Video download failed: Connection timeout",
  "ot_traceid": "4bf92f98e5ed2d16c1245884b5e07383",
  "ot_spanid": "1234567890abcdef"
}
```

## Response Fields Explanation

### Success Response Fields

- `uuid`: The unique identifier for the job
- `status`: Always "success" for successful responses
- `results`: Contains the detection results
  - `uuid`: The unique identifier for the job (repeated for consistency)
  - `timestamp`: ISO 8601 timestamp when the processing completed
  - `video_url`: The original video URL that was processed
  - `ot_traceid`: OpenTelemetry trace ID (same as in request or generated if not provided)
  - `ot_spanid`: OpenTelemetry span ID (same as in request or generated if not provided)
  - `objects`: Array of detected objects with aggregated results
    - `class_name`: The type of object detected (e.g., "person", "car")
    - `max_confidence`: The highest confidence value among all detections of this object type
    - `bounding_box`: The detection with the highest confidence value
      - `bounding_box`: The coordinates of the bounding box
        - `x`: X-coordinate of the top-left corner
        - `y`: Y-coordinate of the top-left corner
        - `width`: Width of the bounding box
        - `height`: Height of the bounding box
      - `frame_number`: The frame number where this detection occurred
      - `timestamp`: The timestamp in seconds where this detection occurred
      - `confidence`: The confidence value for this specific detection
    - `image_url`: URL to the cropped image of this object (only present if `detected_image_export` was true in request)
  - `faces`: Array of recognized faces with aggregated results
    - `name`: The name of the recognized person or "unknown" if not recognized
    - `max_confidence`: The highest confidence value among all detections of this face
    - `bounding_box`: The detection with the highest confidence value
    - `image_url`: URL to the cropped image of this face (only present if `detected_image_export` was true in request)
  - `license_plates`: Array of recognized license plates with aggregated results
    - `text`: The recognized license plate text
    - `max_confidence`: The highest confidence value among all detections of this license plate
    - `bounding_box`: The detection with the highest confidence value
    - `image_url`: URL to the cropped image of this license plate (only present if `detected_image_export` was true in request)
  - `barcodes`: Array of recognized barcodes with aggregated results
    - `text`: The decoded barcode content
    - `barcode_type`: The type of barcode (e.g., "QR_CODE", "CODE_128")
    - `max_confidence`: The highest confidence value among all detections of this barcode
    - `bounding_box`: The detection with the highest confidence value
    - `image_url`: URL to the cropped image of this barcode (only present if `detected_image_export` was true in request)
- `error`: Always null for successful responses
- `ot_traceid`: OpenTelemetry trace ID (same as in request or generated if not provided)
- `ot_spanid`: OpenTelemetry span ID (same as in request or generated if not provided)

### Error Response Fields

- `uuid`: The unique identifier for the job
- `status`: Always "failed" for error responses
- `results`: Always null for error responses
- `error`: A descriptive error message explaining what went wrong
- `ot_traceid`: OpenTelemetry trace ID (same as in request or generated if not provided)
- `ot_spanid`: OpenTelemetry span ID (same as in request or generated if not provided)