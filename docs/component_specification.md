# Component Specification

## 1. FastAPI Application (app/main.py)

### Endpoint
- **URL**: `/detect`
- **Method**: POST
- **Request Body**:
  ```json
  {
    "uuid": "string",
    "video_url": "string",
    "callback_url": "string",
    "object_detection_enabled": true,
    "object_detection_confidence_threshold": 0.5,
    "face_recognition_enabled": true,
    "face_recognition_confidence_threshold": 0.6,
    "license_plate_recognition_enabled": true,
    "license_plate_recognition_confidence_threshold": 0.5,
    "barcode_recognition_enabled": true,
    "barcode_recognition_confidence_threshold": 0.8,
    "detected_image_export": false
  }
  ```
- **Headers** (Optional):
  - `ot_traceid`: OpenTelemetry trace ID
  - `ot_spanid`: OpenTelemetry span ID
- **Response**:
  ```json
  {
    "status": "queued",
    "task_id": "string",
    "uuid": "string"
  }
  ```
- **Error Response (Job Already Exists)**:
  ```json
  {
    "status": "error",
    "message": "job already exists"
  }
  ```

### Statistical Endpoints
- **GET /stats/queue**: Returns number of jobs in queue
- **GET /stats/queue/list**: Returns list of jobs in queue
- **GET /stats/completed**: Returns number of completed jobs
- **GET /stats/failed**: Returns list of failed jobs
- **GET /stats/processing**: Returns number of jobs currently processing
- **GET /stats/workers**: Returns list of active workers with details
- **GET /stats/job/{uuid}**: Returns detailed history for a specific job

### Responsibilities
1. Receive incoming requests with UUID, video URL, callback URL, and enable flags with confidence thresholds
2. Validate input data (including MP4 file validation)
3. Check if job with UUID already exists
4. Generate OpenTelemetry trace/span IDs if not provided
5. Queue task using Celery if job doesn't exist
6. Return task ID and status or error if job exists
7. Provide statistical endpoints for monitoring

## 2. Authentication (app/auth.py)

### JWT Authentication
- Optional authentication that can be enabled/disabled via configuration
- Validates JWT tokens when enabled
- Returns 401 Unauthorized for invalid tokens
- Allows requests to proceed when authentication is disabled

## 3. Configuration (config/settings.py)

### Settings
- RabbitMQ broker URL: `amqp://guest:guest@localhost:5672//`
- Task serializer: JSON
- Result serializer: JSON
- Accept content: JSON
- JWT authentication enabled: Boolean (default: False)
- JWT secret key: String
- Default YOLO detection classes: List of strings
- Default face recognition settings: Configuration options
- Default license plate recognition settings: Configuration options
- Default barcode recognition settings: Configuration options
- Image export settings: S3 configuration options
- YOLO device setting: "cpu" or "cuda"
- Logging level: String (DEBUG, INFO, WARNING, ERROR)
- Log file path: String
- OpenTelemetry enabled: Boolean (default: False)
- OpenTelemetry collector URL: String (optional)

## 4. Celery Tasks (app/tasks.py)

### Task: process_video
- **Parameters**: uuid, video_url, callback_url, object_detection_enabled, object_detection_confidence_threshold, face_recognition_enabled, face_recognition_confidence_threshold, license_plate_recognition_enabled, license_plate_recognition_confidence_threshold, barcode_recognition_enabled, barcode_recognition_confidence_threshold, detected_image_export, ot_traceid, ot_spanid
- **Steps**:
  1. Set up OpenTelemetry tracing context
  2. Download video from URL
  3. Validate that downloaded file is MP4
  4. Process video with YOLO object detection (if enabled in request, using configured device)
  5. Process video with face recognition (if enabled in request)
  6. Process video with license plate recognition (if enabled in request)
  7. Process video with barcode recognition (if enabled in request)
  8. Export detected images to S3 (if requested)
  9. Aggregate detection results by taking highest confidence for each object type
  10. Format results with UUID and OpenTelemetry context
  11. Send results to callback URL
  12. Handle errors and retries
  13. Clean up downloaded video file and temporary images

## 5. Object Detection (app/detection.py)

### Function: detect_objects
- **Parameters**: video_path, detection_classes, confidence_threshold, device
- **Returns**: List of detected objects with timestamps
- **Process**:
  1. Load YOLO model with specified classes and device
  2. Process video frames
  3. Detect objects in each frame (filtered by specified classes and confidence threshold)
  4. Return structured results

## 6. Face Recognition (app/face_recognition.py)

### Function: recognize_faces
- **Parameters**: video_path, confidence_threshold
- **Returns**: List of recognized faces with timestamps
- **Process**:
  1. Load face recognition model
  2. Process video frames
  3. Detect faces in each frame
  4. Recognize known faces or label as "unknown" (filtered by confidence threshold)
  5. Return structured results

## 7. License Plate Recognition (app/license_plate.py)

### Function: recognize_license_plates
- **Parameters**: video_path, confidence_threshold
- **Returns**: List of recognized license plates with timestamps
- **Process**:
  1. Load license plate recognition model
  2. Process video frames
  3. Detect license plates in each frame
  4. Recognize characters on license plates (filtered by confidence threshold)
  5. Return structured results

## 8. Barcode Recognition (app/barcode.py)

### Function: recognize_barcodes
- **Parameters**: video_path, confidence_threshold
- **Returns**: List of recognized barcodes with timestamps
- **Process**:
  1. Load barcode recognition library
  2. Process video frames
  3. Detect barcodes in each frame
  4. Decode barcode content (filtered by confidence threshold)
  5. Return structured results

## 9. Image Export (app/image_export.py)

### Function: export_detected_images
- **Parameters**: video_path, detection_result, uuid, s3_config
- **Returns**: Dictionary mapping detection IDs to S3 URLs
- **Process**:
  1. Extract frames from video at detection timestamps
  2. Crop images based on bounding box coordinates
  3. Upload images to S3-compatible storage
  4. Generate and return access URLs

## 10. Result Aggregation (app/aggregation.py)

### Function: aggregate_results
- **Parameters**: detection_result, image_urls, ot_traceid, ot_spanid
- **Returns**: Aggregated detection result with highest confidence values and image URLs
- **Process**:
  1. Group detections by object type/name
  2. For each group, find the detection with highest confidence
  3. If multiple detections have the same highest confidence, select the first one
  4. Add image URLs to aggregated results
  5. Return aggregated results with OpenTelemetry context

## 11. Worker (worker/worker.py)

### Responsibilities
1. Initialize Celery worker
2. Connect to RabbitMQ
3. Process queued tasks
4. Handle graceful shutdown
5. Report worker status to monitoring system

## 12. Job Tracking (app/job_tracker.py)

### Responsibilities
1. Track job UUIDs to prevent duplicates
2. Store job status information
3. Clean up old job records
4. Provide statistics for monitoring endpoints
5. Store detailed job history with step timing

## 13. OpenTelemetry Integration (app/telemetry.py)

### Responsibilities
1. Generate trace/span IDs if not provided
2. Propagate tracing context to Celery tasks
3. Instrument FastAPI endpoints
4. Instrument Celery task execution
5. Export traces to OpenTelemetry collector (if configured)

## 14. File Management (app/file_utils.py)

### Responsibilities
1. Download files from URLs
2. Validate MP4 file format
3. Clean up temporary files after processing
4. Handle file download errors

## 15. Error Handling

### Types of Errors
1. Invalid input data
2. Video download failures
3. MP4 file validation failures
4. Object detection failures
5. Face recognition failures
6. License plate recognition failures
7. Barcode recognition failures
8. Image export failures
9. Callback URL failures
10. RabbitMQ connection issues
11. Authentication failures (when enabled)
12. Job duplication errors
13. OpenTelemetry configuration errors

### Retry Strategy
- Video download: 3 retries with exponential backoff
- Callback URL: 3 retries with exponential backoff
- Image export: 3 retries with exponential backoff
- Other errors: Log and fail

## 16. Logging

### Log Levels
- DEBUG: Detailed information for diagnosing problems
- INFO: Task start/end, successful operations
- WARNING: Retry attempts, non-critical issues
- ERROR: Failed operations, exceptions
- CRITICAL: Serious errors that may cause application failure

### Log Format
```
[timestamp] [level] [task_id] [uuid] [trace_id] [span_id] message
```

### Log Components
1. File logging to configured path
2. Console logging for development
3. Structured logging for better parsing
4. Log rotation to prevent disk space issues
5. OpenTelemetry trace context inclusion