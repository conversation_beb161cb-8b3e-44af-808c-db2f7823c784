# Image Export Specification

## Overview

The application can optionally export cropped images of detected objects, faces, license plates, and barcodes to an S3-compatible storage when requested. This feature is enabled by setting `detected_image_export: true` in the request.

## Image Export Process

### 1. Image Cropping
- Extract frames from video at detection timestamps
- Crop images based on bounding box coordinates
- Apply padding to bounding boxes for better context
- Save cropped images to temporary storage

### 2. Image Processing
- Resize images to standard dimensions (configurable)
- Apply compression to reduce file size
- Add metadata (detection confidence, timestamp, etc.)
- Convert to standard format (JPEG/PNG)

### 3. S3 Upload
- Upload images to S3-compatible storage
- Use configured bucket and path structure
- Generate unique filenames with UUID prefix
- Set appropriate access permissions

### 4. URL Generation
- Generate public access URLs for uploaded images
- Include URLs in response JSON
- Handle URL expiration for temporary access

## Configuration Options

```yaml
image_export:
  enabled: true
  s3:
    endpoint_url: "https://s3.amazonaws.com"
    access_key_id: "your-access-key"
    secret_access_key: "your-secret-key"
    region_name: "us-east-1"
    bucket_name: "object-detection-images"
    path_prefix: "detections"
  image:
    format: "JPEG"  # or "PNG"
    quality: 85
    max_width: 800
    max_height: 600
    padding: 20  # pixels around bounding box
  url:
    expiration_hours: 24
    public_access: true
```

## Path Structure

Images are stored in the following path structure:
```
{bucket_name}/{path_prefix}/{uuid}/{object_type}/{timestamp}_{confidence}.{format}
```

Example:
```
object-detection-images/detections/550e8400-e29b-41d4-a716-************/objects/1.5_0.95.jpg
```

## Filename Convention

Filenames follow this convention:
```
{timestamp}_{confidence}.{format}
```

Where:
- `timestamp`: Detection timestamp in seconds
- `confidence`: Detection confidence value (0.00-1.00)
- `format`: Image format (jpg, png)

## Security Considerations

### Authentication
- S3 credentials stored securely in configuration
- Environment variables for sensitive data
- IAM roles for containerized deployments

### Access Control
- Configurable public/private access
- URL expiration for temporary access
- Bucket policies for additional security

### Data Protection
- Encryption at rest (S3 server-side encryption)
- Encryption in transit (HTTPS)
- Temporary file cleanup after upload

## Performance Considerations

### Processing
- Parallel image processing for multiple detections
- Memory-efficient image handling
- Streaming uploads to S3

### Storage
- Configurable image quality/compression
- Automatic cleanup of temporary files
- Monitoring of storage usage

## Error Handling

### Common Errors
1. **S3 authentication failure**: Log error and continue without image export
2. **Upload failure**: Retry with exponential backoff
3. **Image processing failure**: Skip individual image, continue with others
4. **Storage quota exceeded**: Alert operators and disable image export

### Fallback Mechanisms
- Disable image export if persistent errors occur
- Log errors and continue with detection processing
- Alert operators on configuration issues

## Data Models

### Updated Aggregated Detection Models
```python
class AggregatedDetectedObject(BaseModel):
    class_name: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]  # URL to cropped image

class AggregatedRecognizedFace(BaseModel):
    name: str  # "unknown" for unrecognized faces
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]  # URL to cropped image

class AggregatedRecognizedLicensePlate(BaseModel):
    text: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]  # URL to cropped image

class AggregatedRecognizedBarcode(BaseModel):
    text: str
    barcode_type: str  # "QR_CODE", "CODE_128", etc.
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]  # URL to cropped image
```

## Integration Points

### Detection Modules
- Object detection module crops and exports object images
- Face recognition module crops and exports face images
- License plate recognition module crops and exports plate images
- Barcode recognition module crops and exports barcode images

### S3 Integration
- boto3 library for S3 operations
- Asynchronous uploads to avoid blocking detection processing
- Connection pooling for efficient S3 access

## Testing

### Unit Tests
- Image cropping accuracy
- S3 upload functionality
- URL generation
- Error handling scenarios

### Integration Tests
- End-to-end image export workflow
- S3 connectivity and authentication
- Image quality and format verification

### Performance Tests
- Image processing time
- Upload speed and bandwidth usage
- Memory usage during image processing