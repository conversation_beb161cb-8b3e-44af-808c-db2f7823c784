# Local Development Guide

This guide explains how to run the Object Detection Service locally on your computer.

## Prerequisites

Before running the service locally, ensure you have:

1. Python 3.11 installed
2. RabbitMQ server installed and running
3. MongoDB server installed and running
4. (Optional) MinIO or another S3-compatible service for image export

## Installation Steps

### 1. Install Dependencies

```bash
# Create a virtual environment (recommended)
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install required packages
pip install -r requirements.txt
```

### 2. Start Required Services

You'll need to run RabbitMQ, MongoDB (and optionally MinIO) before starting the application.

#### Option A: Using Docker (Recommended)

```bash
# Start RabbitMQ
docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management

# Start MongoDB
docker run -d --name mongodb -p 27017:27017 -e MONGO_INITDB_ROOT_USERNAME=admin -e MONGO_INITDB_ROOT_PASSWORD=password mongo:5.0

# (Optional) Start MinIO for S3-compatible storage
docker run -d --name minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  quay.io/minio/minio server /data --console-address ":9001"
```

#### Option B: Using System Services

If you have RabbitMQ and MongoDB installed locally:

```bash
# Start RabbitMQ service (method varies by OS)
# On macOS with Homebrew:
brew services start rabbitmq

# On Ubuntu/Debian:
sudo systemctl start rabbitmq-server

# Start MongoDB service
# On macOS with Homebrew:
brew services start mongodb-community

# On Ubuntu/Debian:
sudo systemctl start mongod

# On Windows:
# Start RabbitMQ and MongoDB services from Services management console
```

## Running the Application

### 1. Start the FastAPI Application

```bash
# Using the local configuration file
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

Alternatively, you can set the configuration path as an environment variable:

```bash
# Run the application
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. Start the Celery Worker

In a separate terminal window/tab:

```bash
# Using the local configuration file
celery -A app.celery_app worker --loglevel=info --queues=video_processing
```

Alternatively, with the environment variable:

```bash
# Start the worker
celery -A app.celery_app worker --loglevel=info --queues=video_processing
```

## Testing the Application

### 1. Health Check

```bash
# Check if the API is running
curl http://localhost:8000/health
```

### 2. Submit a Detection Request

```bash
# Example request (replace with actual video URL and callback URL)
curl -X POST http://localhost:8000/detect \
  -H "Content-Type: application/json" \
  -d '{
    "uuid": "test-job-001",
    "video_url": "https://example.com/sample-video.mp4",
    "callback_url": "https://your-callback-endpoint.com/results",
    "object_detection_enabled": true,
    "face_recognition_enabled": true,
    "license_plate_recognition_enabled": true,
    "barcode_recognition_enabled": true,
    "detected_image_export": false
  }'
```

### 3. Check Queue Statistics

```bash
# Get queue information
curl http://localhost:8000/stats/queue

# Get list of queued jobs
curl http://localhost:8000/stats/queue/list

# Get number of completed jobs
curl http://localhost:8000/stats/completed

# Get list of failed jobs
curl http://localhost:8000/stats/failed
```


## Known Faces Setup

To use face recognition:

1. Create subdirectories in the `known_faces` directory for each person
2. Add reference images to each person's directory
3. The system will automatically generate encodings when it starts

Example structure:
```
known_faces/
├── john_doe/
│   ├── photo1.jpg
│   ├── photo2.jpg
│   └── photo3.jpg
├── jane_smith/
│   ├── photo1.jpg
│   └── photo2.jpg
└── encodings.json  # Auto-generated
```

## Troubleshooting

### Common Issues

1. **RabbitMQ Connection Failed**
   - Ensure RabbitMQ is running
   - Check RabbitMQ credentials in config.yaml
   - Verify network connectivity to RabbitMQ

2. **MongoDB Connection Failed**
   - Ensure MongoDB is running
   - Check MongoDB credentials in config.yaml
   - Verify network connectivity to MongoDB

3. **Model Download Issues**
   - Check internet connectivity
   - Verify disk space availability
   - Check firewall settings

4. **CUDA Errors (if using GPU)**
   - Ensure NVIDIA drivers are installed
   - Verify CUDA toolkit installation
   - Check GPU memory availability

### Logs

Check logs for debugging information:

```bash
# View application logs
tail -f logs/app.log

# View worker logs (if redirected)
tail -f logs/worker.log
```

## Development Workflow

1. Make code changes
2. The application will auto-reload (due to `--reload` flag)
3. Test your changes
4. Check logs for any errors

## Stopping the Services

To stop the services:

1. Press `Ctrl+C` in the terminal windows running the FastAPI app and Celery worker
2. Stop Docker containers (if used):

```bash
# Stop individual containers
docker stop rabbitmq mongodb minio

# Remove containers
docker rm rabbitmq mongodb minio
```

3. Deactivate virtual environment:

```bash
deactivate