# Logging Specification

## Logging Levels

The application uses the standard Python logging levels:

1. **DEBUG** - Detailed information, typically of interest only when diagnosing problems
2. **INFO** - Confirmation that things are working as expected
3. **WARNING** - An indication that something unexpected happened
4. **ERROR** - Due to a more serious problem, the software has not been able to perform some function
5. **CRITICAL** - A serious error, indicating that the program itself may be unable to continue running

## Log Format

All logs follow a consistent format:

```
[timestamp] [level] [module] [task_id] [uuid] message
```

Example:
```
[2023-05-15 14:30:22,123] [INFO] [main] [task_12345] [uuid-abc-123] Task queued successfully
```

## Log Components

### 1. Timestamp
- Format: `YYYY-MM-DD HH:MM:SS,mmm`
- Always in UTC
- Millisecond precision

### 2. Level
- One of: DEBUG, INFO, WARNING, ERROR, CRITICAL
- Color-coded in console output

### 3. Module
- Name of the module that generated the log
- Helps in identifying the source of the log

### 4. Task ID
- Celery task identifier
- Empty for application startup/shutdown logs
- Helps in tracking task-specific logs

### 5. UUID
- Job UUID from the request
- Empty for system-level logs
- Helps in tracking job-specific logs

### 6. Message
- Descriptive message about the event
- May include additional context information


## Console Logging

### Console Logging
- All logs are printed to console
- Color-coded by log level
- Same format as previous file logs

## Structured Logging

For better parsing and analysis, structured logs are also generated for key events:

```json
{
  "timestamp": "2023-05-15T14:30:22.123Z",
  "level": "INFO",
  "module": "main",
  "task_id": "task_12345",
  "uuid": "uuid-abc-123",
  "event": "task_queued",
  "message": "Task queued successfully",
  "details": {
    "video_url": "https://example.com/video.mp4",
    "callback_url": "https://example.com/callback"
  }
}
```

## Log Categories

### Application Lifecycle
- Application startup and shutdown
- Configuration loading
- Component initialization

### Request Handling
- Incoming requests
- Authentication status
- Request validation
- Response sending

### Task Management
- Task queuing
- Task execution start/end
- Task results sending
- Task errors and retries

### External Services
- RabbitMQ connection status
- Video download progress
- Callback URL communication
- YOLO model loading

### Error Handling
- Exception details
- Error recovery attempts
- Failed operations

## Performance Considerations

### Log Filtering
- Logs can be filtered by level, module, or custom filters
- Sensitive information is automatically masked