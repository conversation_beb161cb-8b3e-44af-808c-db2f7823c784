# Object Detection Application Architecture

## System Components

```mermaid
graph TD
    A[Client] -->|POST /detect| B(FastAPI Server)
    B -->|Validate Auth| B
    B -->|Check Job Exists| C[(Job Tracker)]
    B -->|Validate MP4| D[File Validation]
    B -->|Generate OT IDs| E[OpenTelemetry]
    B -->|Queue Task| F[(RabbitMQ Broker)]
    G[Celery Worker] -->|Fetch Task| F
    G -->|Download Video| H[Video URL]
    G -->|Validate MP4| H
    G -->|Process with YOLO| I[YOLO Model]
    G -->|Process Faces| J[Face Recognition]
    G -->|Process Plates| K[License Plate Recognition]
    G -->|Process Barcodes| L[Barcode Recognition]
    G -->|POST Results| M[Callback URL]
    G -->|Cleanup Files| N[File Cleanup]
    O[Docker Container] -->|Contains| B
    O -->|Contains| G
    P[OTLP Collector] -->|Receive Traces| E
```

## Data Flow

1. Client sends a POST request to FastAPI endpoint with:
   - UUID
   - Video URL
   - Callback URL
   - Enable flags and confidence thresholds for all recognition processes
   - Optional OpenTelemetry headers (ot_traceid, ot_spanid)
   - Optional JWT Token (if authentication enabled)

2. FastAPI validates authentication if enabled

3. FastAPI checks if job with UUID already exists

4. FastAPI validates that URL points to an MP4 file

5. FastAPI generates OpenTelemetry trace/span IDs if not provided

6. If job exists, return error; otherwise, create task and add it to RabbitMQ queue via Celery

7. Celery worker picks up the task from RabbitMQ

8. Worker downloads the video from the provided URL

9. Worker validates that downloaded file is MP4

10. Worker processes the video using YOLO for object detection (if enabled)

11. Worker processes the video for face recognition (if enabled)

12. Worker processes the video for license plate recognition (if enabled)

13. Worker processes the video for barcode recognition (if enabled)

14. Worker formats results with UUID and sends them to the callback URL

15. Worker cleans up downloaded video file

## Project Structure

```
object_detection_app/
├── app/
│   ├── main.py              # FastAPI application
│   ├── tasks.py             # Celery task definitions
│   ├── detection.py         # YOLO object detection logic
│   ├── face_recognition.py   # Face recognition logic
│   ├── license_plate.py     # License plate recognition logic
│   ├── barcode.py           # Barcode recognition logic
│   ├── auth.py              # JWT authentication
│   ├── job_tracker.py       # Job tracking and deduplication
│   ├── telemetry.py         # OpenTelemetry integration
│   └── file_utils.py        # File handling utilities
├── worker/
│   └── worker.py            # Celery worker configuration
├── config/
│   ├── settings.py          # Application settings
│   └── config.yaml          # Configuration file
├── known_faces/             # Known faces for recognition
│   ├── person1/              # Reference images for person1
│   ├── person2/              # Reference images for person2
│   └── encodings.json       # Pre-computed face encodings
├── logs/                    # Log directory
├── requirements.txt         # Python dependencies
├── Dockerfile               # Docker configuration
└── docker-compose.yml       # Multi-container setup
```

## Technologies Used

- **FastAPI**: Web framework for REST API
- **Celery**: Distributed task queue
- **RabbitMQ**: Message broker for Celery
- **YOLO**: Object detection model
- **face-recognition**: Face recognition library
- **EasyOCR**: License plate recognition
- **PyZBar**: Barcode recognition
- **PyJWT**: JWT token handling
- **PyYAML**: Configuration file parsing
- **OpenTelemetry**: Distributed tracing
- **Docker**: Containerization

## Dependencies

```
fastapi>=0.68.0
uvicorn>=0.15.0
celery>=5.2.0
pika>=1.3.0  # RabbitMQ client
ultralytics>=8.0.0  # YOLOv8
face-recognition>=1.3.0  # Face recognition
easyocr>=1.6.0  # License plate recognition
pyzbar>=0.1.9  # Barcode recognition
requests>=2.28.0
python-multipart>=0.0.5
pyjwt>=2.4.0
pyyaml>=6.0
opentelemetry-api>=1.12.0
opentelemetry-sdk>=1.12.0
opentelemetry-exporter-otlp>=1.12.0
opentelemetry-instrumentation-fastapi>=0.33b0
opentelemetry-instrumentation-celery>=0.33b0
python-magic>=0.4.27  # File type detection