# Data Models

## Request Model

```python
class DetectionRequest(BaseModel):
    uuid: str
    video_url: str
    callback_url: str
    object_detection_enabled: Optional[bool] = True
    object_detection_confidence_threshold: Optional[float] = 0.5
    face_recognition_enabled: Optional[bool] = True
    face_recognition_confidence_threshold: Optional[float] = 0.6
    license_plate_recognition_enabled: Optional[bool] = True
    license_plate_recognition_confidence_threshold: Optional[float] = 0.5
    barcode_recognition_enabled: Optional[bool] = True
    barcode_recognition_confidence_threshold: Optional[float] = 0.8
    detected_image_export: Optional[bool] = False

class DetectionRequestHeaders(BaseModel):
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None
```

## Response Model

```python
class DetectionResponse(BaseModel):
    status: str
    task_id: str
    uuid: str

class ErrorResponse(BaseModel):
    status: str
    message: str

class QueueStatsResponse(BaseModel):
    count: int

class JobListResponse(BaseModel):
    jobs: List[JobInfo]

class JobInfo(BaseModel):
    uuid: str
    status: str
    created_at: datetime

class WorkerInfoResponse(BaseModel):
    workers: List[WorkerInfo]

class WorkerInfo(BaseModel):
    id: str
    hostname: str
    pid: int
    active_tasks: int
    processed_tasks: int
    status: str
    last_heartbeat: datetime

class JobHistoryResponse(BaseModel):
    job: JobHistory

class JobHistory(BaseModel):
    uuid: str
    status: str
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    steps: List[JobStep]
    response_data: Optional[str]
    error_message: Optional[str]

class JobStep(BaseModel):
    name: str
    started_at: datetime
    completed_at: datetime
    duration_ms: int
    status: str
    details: Optional[dict]
```

## Authentication Models

```python
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str
```

## Task Parameters

```python
class TaskParameters:
    uuid: str
    video_url: str
    callback_url: str
    object_detection_enabled: bool
    object_detection_confidence_threshold: float
    face_recognition_enabled: bool
    face_recognition_confidence_threshold: float
    license_plate_recognition_enabled: bool
    license_plate_recognition_confidence_threshold: float
    barcode_recognition_enabled: bool
    barcode_recognition_confidence_threshold: float
    detected_image_export: bool
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None
```

## Detection Result Model

```python
class DetectionResult(BaseModel):
    uuid: str
    timestamp: str
    objects: Optional[List[DetectedObject]]
    faces: Optional[List[RecognizedFace]]
    license_plates: Optional[List[RecognizedLicensePlate]]
    barcodes: Optional[List[RecognizedBarcode]]
    video_url: str

class DetectedObject(BaseModel):
    class_name: str
    confidence: float
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float

class RecognizedFace(BaseModel):
    name: str  # "unknown" for unrecognized faces
    confidence: float
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float

class RecognizedLicensePlate(BaseModel):
    text: str
    confidence: float
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float

class RecognizedBarcode(BaseModel):
    text: str
    barcode_type: str  # "QR_CODE", "CODE_128", etc.
    confidence: float
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float

class BoundingBox(BaseModel):
    x: float
    y: float
    width: float
    height: float
```

## Callback Payload

```python
class CallbackPayload(BaseModel):
    uuid: str
    status: str  # success, failed
    results: Optional[AggregatedDetectionResult]
    error: Optional[str]
    ot_traceid: Optional[str]
    ot_spanid: Optional[str]
```

## Aggregated Detection Result Model

```python
class AggregatedDetectionResult(BaseModel):
    uuid: str
    timestamp: str
    objects: Optional[List[AggregatedDetectedObject]]
    faces: Optional[List[AggregatedRecognizedFace]]
    license_plates: Optional[List[AggregatedRecognizedLicensePlate]]
    barcodes: Optional[List[AggregatedRecognizedBarcode]]
    video_url: str
    ot_traceid: Optional[str]
    ot_spanid: Optional[str]

class AggregatedDetectedObject(BaseModel):
    class_name: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class BoundingBoxWithFrame(BaseModel):
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float
    confidence: float

class AggregatedRecognizedFace(BaseModel):
    name: str  # "unknown" for unrecognized faces
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedLicensePlate(BaseModel):
    text: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedBarcode(BaseModel):
    text: str
    barcode_type: str  # "QR_CODE", "CODE_128", etc.
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]