from fastapi import <PERSON>AP<PERSON>, HTTPException, Header, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
import uuid
import logging
import jwt
from jwt.exceptions import (
    DecodeError,
    ExpiredSignatureError,
    InvalidTokenError,
    InvalidSignatureError
)
import time
from datetime import timezone
import os, json
from app.models import TaskParameters, DetectionRequest, DetectionResponse, ErrorResponse, QueueStatsResponse, JobListResponse, WorkerInfoResponse, JobHistoryResponse
from app.config.settings import config
from app.celery_app import process_video, celery_app
from app.job_tracking import job_tracker
from app.database import db_manager

# Initialize FastAPI app
app = FastAPI(
    title="Object Detection Service",
    description="A service for detecting objects, faces, license plates, and barcodes in videos",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

def verify_auth_token(authorization: Optional[str] = Header(None)):
    """Verify JWT token if authentication is enabled."""
    auth_enabled = config.get("auth.enabled", False)
    if not auth_enabled:
        return True
    
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    # In a real implementation, we would verify the JWT token here
    # For now, we'll just check if it starts with "Bearer "
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    return True

def get_auth_dependency():
    """Return auth dependency based on config."""
    auth_enabled = config.get("auth.enabled", False)
    if auth_enabled:
        return Depends(verify_auth_token)
    else:
        return None

def find_existing(base_str, values_list):
    base_items = base_str.strip(',').split(',')
    found = [v for v in values_list if v in base_items]
    return ','.join(found)

def decode_jwt(token: str):
    try:
        jwt_secret = config.get("auth.secret_key", "")
        if not jwt_secret:
            raise jwt.InvalidTokenError("JWT secret key is not configured")

        jwt_algorithm = config.get("auth.algorithm", "HS256")
        allowed_algorithms = ["HS256", "HS384", "HS512", "RS256", "RS384", "RS512"]
        if jwt_algorithm not in allowed_algorithms:
            raise jwt.InvalidTokenError(f"Unsupported JWT algorithm: {jwt_algorithm}")

        # Token format validation
        parts = token.split('.')
        if len(parts) != 3:
            raise jwt.DecodeError("Invalid token format")

        # Decode and verify token
        decoded_token = jwt.decode(
            token,
            jwt_secret,
            algorithms=[jwt_algorithm],
            options={
                'verify_signature': True,
                'verify_exp': True,
                'verify_iat': True,
                'verify_nbf': True,
                'require': ['exp', 'iat', 'sub', 'iss']
            }
        )

        # Validate required claims
        required_claims = ['sub', 'iss', 'iat', 'exp']
        for claim in required_claims:
            if claim not in decoded_token:
                raise jwt.InvalidTokenError(f"Missing required claim: {claim}")

        # Validate issuer
        allowed_issuers = config.get("auth.allowed_issuers", ["ezlo-auth-service"])
        if decoded_token['iss'] not in allowed_issuers:
            raise jwt.InvalidTokenError(f"Invalid token issuer: {decoded_token['iss']}")

        # Validate timestamps
        iat = decoded_token['iat']
        exp = decoded_token['exp']
        nbf = decoded_token.get('nbf', iat)  # Not Before Time

        current_time = int(time.time())
        if current_time < nbf:
            raise jwt.InvalidTokenError("Token is not yet valid")

        if isinstance(iat, (int, float)):
            iat = datetime.datetime.fromtimestamp(iat, tz=timezone.utc)
        if isinstance(exp, (int, float)):
            exp = datetime.datetime.fromtimestamp(exp, tz=timezone.utc)

        # Validate token duration
        token_duration = exp - iat
        max_duration = datetime.timedelta(hours=24)
        min_duration = datetime.timedelta(minutes=5)

        if token_duration > max_duration:
            logger.error(f"Token validity ({token_duration}) exceeds maximum allowed ({max_duration})")
            raise jwt.ExpiredSignatureError(
                f"Token validity ({token_duration}) exceeds maximum allowed ({max_duration})"
            )

        if token_duration < min_duration:
            logger.error(f"Token validity ({token_duration}) is less than minimum required ({min_duration})")
            raise jwt.InvalidTokenError(
                f"Token validity ({token_duration}) is less than minimum required ({min_duration})"
            )

        if datetime.datetime.now(timezone.utc) > exp:
            raise jwt.ExpiredSignatureError("Token has expired")

        # Rate limiting and token reuse prevention could be added here
        # This would require a cache/database to track used tokens

        return decoded_token

    except ExpiredSignatureError:
        logger.warning("JWT token has expired")
        raise
    except InvalidSignatureError:
        logger.warning("Invalid JWT signature detected")
        raise
    except DecodeError as e:
        logger.warning(f"JWT decode error: {str(e)}")
        raise
    except InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during JWT validation: {str(e)}")
        raise jwt.InvalidTokenError("Token validation failed")

def generate_ids():
    import secrets
    span_id = secrets.token_hex(8).upper()
    trace_id = secrets.token_hex(16).upper()

    return str(span_id), str(trace_id)

def prepare_task_params(payload,auth_enabled=False,authorization=None):
    try:
        resource_method = ""
        resource_url = ""
        rqresponse_callback = ""
        rqfile_filename = ""
        refile_uuid = ""

        hostname = os.uname().nodename

        # ot_trace_id = ""
        # ot_span_id = ""
        ReqOpenTelemetry = payload.ot
        if ReqOpenTelemetry is not None:
            ot_span_id = ReqOpenTelemetry.span_id
            ot_trace_id = ReqOpenTelemetry.trace_id
        else:
            ot_span_id, ot_trace_id = generate_ids()

        ReqResources = payload.resources
        if ReqResources is not None:
            resource_method = ReqResources.method
            resource_url = ReqResources.url

        rqfile_meta = None
        rqfile_meta_controller_uuid = ""
        rqfile_meta_device_id = ""
        ReqFile = payload.file
        if ReqFile is not None:
            refile_uuid = ReqFile.uuid
            # rqfile_timestamp = ReqFile.timestamp
            # rqfile_size = ReqFile.size
            # rqfile_format = ReqFile.file_format
            rqfile_filename = ReqFile.filename
            rqfile_meta = ReqFile.meta

        if rqfile_meta is None or rqfile_meta == "":
            logger.debug(f"meta is not json format uuid:{refile_uuid}")
        else:
            try:
                rqfile_meta_json = json.loads(rqfile_meta)
                rqfile_meta_controller_info = rqfile_meta_json.get("controller_info",{})
                rqfile_meta_controller_uuid = rqfile_meta_controller_info.get("controller_uuid","")
                rqfile_meta_device_id = rqfile_meta_controller_info.get("device_id", "")
            except json.JSONDecodeError as e:
                logger.debug(f"meta is not json format uuid:{refile_uuid}")

        if auth_enabled:
            token = authorization.split(" ")[1]
            try:
                decode_jwt(token)
            except jwt.ExpiredSignatureError:
                raise
            except jwt.InvalidSignatureError:
                raise
            except jwt.DecodeError:
                raise
            except jwt.InvalidTokenError:
                raise

        plate_detection = False
        plate_detection_filter_items = None
        person_detection = False
        package_detection = False
        animal_detection = False
        animal_detection_items = None
        face_recognition = False
        face_recognition_filter_items = None
        barcode_detection = False
        barcode_detection_filter_items = None
        vehicle_detection = False
        vehicle_detection_items = None
        hotzone_detection = False
        hotzone_zones = []
        face_recognition_known_people = []
        global_confidence_threshold = config.get("yolo.confidence_threshold", 0.65)
        standard_skip_frame = "0"
        package_skip_frame = "0"

        reqDb = payload.db
        if reqDb is not None:
            rqdb_elements = reqDb.elements
            rqdb_elements_configuration = reqDb.configuration

            if "standard_skip_frame" in rqdb_elements_configuration:
                standard_skip_frame = rqdb_elements_configuration.standard_skip_frame

            if "package_skip_frame" in rqdb_elements_configuration:
                package_skip_frame = rqdb_elements_configuration.package_skip_frame

            if "hotzones" in rqdb_elements_configuration:
                rqdb_elements_configuration_hotzone_detection = rqdb_elements_configuration.hotzones
                hotzone_detection = rqdb_elements_configuration_hotzone_detection.enabled
                hotzone_zones = rqdb_elements_configuration_hotzone_detection.zones

            if "global_threshold" in rqdb_elements_configuration:
                global_confidence_threshold = rqdb_elements_configuration.global_threshold

            if "person_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_person_detection = rqdb_elements_configuration.person_detection
                person_detection = rqdb_elements_configuration_person_detection.enabled

            if "package_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_package_detection = rqdb_elements_configuration.package_detection
                package_detection = rqdb_elements_configuration_package_detection.enabled

            if "animal_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_animal_detection = rqdb_elements_configuration.animal_detection
                animal_detection = rqdb_elements_configuration_animal_detection.enabled
                if "items" in rqdb_elements_configuration_animal_detection:
                    animal_detection_items = rqdb_elements_configuration_animal_detection.items

            if "vehicle_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_vehicle_detection = rqdb_elements_configuration.vehicle_detection
                vehicle_detection = rqdb_elements_configuration_vehicle_detection.enabled
                if "items" in rqdb_elements_configuration_vehicle_detection:
                    vehicle_detection_items = rqdb_elements_configuration_vehicle_detection.items

            if "face_recognition" in rqdb_elements_configuration:
                rqdb_elements_configuration_face_recognition = rqdb_elements_configuration.face_recognition
                face_recognition = rqdb_elements_configuration_face_recognition.enabled
                if "items" in rqdb_elements_configuration_face_recognition:
                    face_recognition_filter_items = rqdb_elements_configuration_face_recognition.items

            if "known_people" in rqdb_elements:
                face_recognition_known_people = rqdb_elements.known_people

            # support for barcode and plate detection has been disabled
            if "barcode_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_barcode_detection = rqdb_elements_configuration.barcode_detection
                # barcode_detection = rqdb_elements_configuration_barcode_detection.enabled
                barcode_detection = False # not supported now
                if "items" in rqdb_elements_configuration_barcode_detection:
                    barcode_detection_filter_items = None  # not supported now
                    # barcode_detection_filter_items = rqdb_elements_configuration_barcode_detection.items

            if "plate_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_plate_detection = rqdb_elements_configuration.plate_detection
                # plate_detection = rqdb_elements_configuration_plate_detection.enabled
                plate_detection = False  # not supported now
                if "items" in rqdb_elements_configuration_plate_detection:
                    plate_detection_filter_items = None  # not supported now
                    # plate_detection_filter_items = rqdb_elements_configuration_plate_detection.items

        reqResponse = payload.response
        if reqResponse is not None:
            rqresponse_callback = reqResponse.callback

        step00_downloadFile_savedFileDir = config.get("file_processing.temp_directory", "/tmp")

        step30_standartDetection_confidenceThreshold = global_confidence_threshold

        step30_ml_device = config.get("yolo.device", "gpu")
        step30_licensePlate_confidenceThreshold = float(config.get("license_plate_recognition.confidence_threshold", 0.69))
        step30_licensePlate_country = config.get("license_plate_recognition.country", "us")
        step30_standardDetection_model = config.get("yolo.model", "ezlomodels/ezlo-yolo-standard-11s.pt")
        step30_packageDetection_model = config.get("package.model", "ezlomodels/ezlo-yolo-package.pt")
        step30_customPackageDetection_model = config.get("custom_package.model", "ezlomodels/ezlo-custom-package.pt")
        step30_lplateDetection_model = config.get("license_plate_recognition.model", "ezlomodels/license_plate_detector.pt")

        person_classes = config.get("yolo.person_detection_classes", "person,")
        package_classes = config.get("package.detection_classes", "parcel,box,bundle,packet,package,crate,carton,envelope,case,kit,bag,")

        licenseplate_classes = config.get("license_plate_recognition.detection_classes", "license plate,")

        animal_classes = config.get("yolo.animal_detection_classes", "cat,dog,")
        vehicle_classes = config.get("yolo.vehicle_detection_classes", "car,motorcycle,")

        if standard_skip_frame == "0":
            standard_skip_frame = config.get("yolo.skip_frame_ratio", "0.5")

        if package_skip_frame == "0":
            package_skip_frame = config.get("package.skip_frame_ratio", "0.5")

        if animal_detection_items is not None:
            if isinstance(animal_detection_items, list):
                if len(animal_detection_items) > 0:
                    animal_classes = find_existing(animal_classes, animal_detection_items)
                    if animal_classes != "":
                        animal_detection = True
                    else:
                        animal_detection = False
                else:
                    animal_detection = False
                    animal_classes = ""

        if vehicle_detection_items is not None:
            if isinstance(vehicle_detection_items, list):
                if len(vehicle_detection_items) > 0:
                    vehicle_classes = find_existing(vehicle_classes, vehicle_detection_items)
                    if vehicle_classes != "":
                        vehicle_detection = True
                    else:
                        vehicle_detection = False
                else:
                    vehicle_detection = False
                    vehicle_classes = ""

        if isinstance(face_recognition_filter_items, list):
            if len(face_recognition_filter_items) > 0:
                face_recognition = True
            else:
                face_recognition = False
        else:
            face_recognition_filter_items = None

        if isinstance(barcode_detection_filter_items, list):
            if len(barcode_detection_filter_items) > 0:
                barcode_detection = True
            else:
                barcode_detection = False
        else:
            barcode_detection_filter_items = None

        payload = {
            "uuid": refile_uuid,
            "response_callback": rqresponse_callback,
            "log_debug": False,
            "step10_downloadFile": {
                "filename": rqfile_filename,
                "resource_method": resource_method,
                "resource_url": resource_url,
                "configuration": {
                    "enabled": True,
                    "savedFileDir": step00_downloadFile_savedFileDir
                }
            },
            "step20_fileValidate": {
                "mp4Check": False,
                "decompress": False,
                "configuration": {
                    "enabled": False
                }
            },
            "step30_objectDetection": {
                "ml_device": step30_ml_device,
                "face_recognition": face_recognition,
                "face_recognition_filter_items": face_recognition_filter_items,
                "face_recognition_known_people": face_recognition_known_people,
                "plate_detection": plate_detection,
                "plate_detection_filter_items": plate_detection_filter_items,
                "person_detection": person_detection,
                "animal_detection": animal_detection,
                "vehicle_detection": vehicle_detection,
                "package_detection": package_detection,
                "barcode_detection": barcode_detection,
                "barcode_detection_filter_items": barcode_detection_filter_items,
                "hotzone_detection": hotzone_detection,
                "person_classes": person_classes,
                "vehicle_classes": vehicle_classes,
                "animal_classes": animal_classes,
                "package_classes": package_classes,
                "licenseplate_classes": licenseplate_classes,
                "configuration": {
                    "enabled": True,
                    "standard_skip_frame": standard_skip_frame,
                    "package_skip_frame": package_skip_frame,
                    "standard_model": step30_standardDetection_model,
                    "package_model": step30_packageDetection_model,
                    "custom_package_model": step30_customPackageDetection_model,
                    "lplate_model": step30_lplateDetection_model,
                    "confidence_threshold": step30_standartDetection_confidenceThreshold,
                    "licensePlateConfidenceThreshold": step30_licensePlate_confidenceThreshold,
                    "licensePlateCountry": step30_licensePlate_country
                },
                "hotzone_zones": hotzone_zones
            },
            "step40_sendResult": {
                "configuration": {
                    "enabled": True
                }
            },
            "step50_cleanEnvironment": {
                "deleteLocalFile": True,
                "configuration": {
                    "enabled": True
                }
            },
            "ot_trace_id": ot_trace_id,
            "ot_span_id": ot_span_id,
            "controller_uuid": rqfile_meta_controller_uuid,
            "device_id": rqfile_meta_device_id,
            "hostname": hostname
        }

        return payload
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"Error in main_helper.prepare_payload: {exc_type} file:{fname} line:{exc_tb.tb_lineno} {e}")
        raise e
        # return None

@app.post("/object_detection", response_model=DetectionResponse, responses={409: {"model": ErrorResponse}})
async def detect_objects(
    request: DetectionRequest,
    authorization: Optional[str] = Header(None)
):
    """
    Submit a video for object detection.

    Args:
        request: Detection request containing video URL, callback URL, and detection configuration
        authorization: Optional JWT token for authentication (required if auth.enabled is true)

    Returns:
        DetectionResponse: Task information
    """
    # Extract UUID from file info
    job_uuid = request.file.uuid

    # Check authentication if enabled
    auth_enabled = config.get("auth.enabled", False)
    if auth_enabled:
        if not authorization:
            return DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="authorization header missing"
            )

        if not authorization.startswith("Bearer "):
            return DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="invalid authorization header"
            )

    # Prepare task parameters
    try:
        task_params = prepare_task_params(request, auth_enabled, authorization)
    except ExpiredSignatureError:
        return DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Token has expired"
        )
    except InvalidSignatureError:
        return DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Invalid token signature"
        )
    except DecodeError:
        return DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Invalid token format"
        )
    except InvalidTokenError:
        return DetectionResponse(
            status="failed",
            status_code=401,
            task_id="",
            uuid=job_uuid,
            message="Invalid token"
        )
    except Exception as e:
        return DetectionResponse(
            status="failed",
            status_code=500,
            task_id="",
            uuid=job_uuid,
            message="Internal server error"
        )

    # Check if job with UUID already exists
    if job_tracker.job_exists(job_uuid):
        logger.warning(f"Job with UUID {job_uuid} already exists")
        return DetectionResponse(
            status="failed",
            status_code=409,
            task_id="",
            uuid=job_uuid,
            message="job already exists"
        )

    if task_params is None:
        return DetectionResponse(
            status="queued",
            status_code=500,
            task_id="",
            uuid=job_uuid,
            message="internal server error"
        )

    # Create job record
    ot_trace_id = request.ot.trace_id
    ot_span_id = request.ot.span_id
    job_tracker.create_job(job_uuid, ot_trace_id, ot_span_id)

    # Queue task
    task = process_video.delay(task_params)
    task_id = task.id

    # Update job state
    job_tracker.update_job_state(job_uuid, "main", "queued", task_id)

    return DetectionResponse(
        status="queued",
        status_code=200,
        task_id=task_id,
        uuid=job_uuid,
        message="job added successfully"
    )

@app.get("/stats/queue", response_model=QueueStatsResponse)
async def get_queue_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_count = job_tracker.get_job_count_by_state("queued")
    return QueueStatsResponse(count=queued_count)

@app.get("/stats/queue/list", response_model=JobListResponse)
async def get_queue_list(authorized: bool = Depends(verify_auth_token)):
    """Get a list of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_jobs = job_tracker.get_jobs_by_state("queued")
    # Convert datetime objects to strings for JSON serialization
    for job in queued_jobs:
        if "created_at" in job and hasattr(job["created_at"], "isoformat"):
            job["created_at"] = job["created_at"].isoformat()
    return JobListResponse(jobs=queued_jobs)

@app.get("/stats/completed", response_model=QueueStatsResponse)
async def get_completed_stats(authorized: bool = Depends(verify_auth_token)):
    completed_count = job_tracker.get_job_count_by_state("completed")
    return QueueStatsResponse(count=completed_count)

@app.get("/stats/failed", response_model=QueueStatsResponse)
async def get_failed_stats(authorized: bool = Depends(verify_auth_token)):
    failed_count = job_tracker.get_job_count_by_state("failed")    
    return QueueStatsResponse(count=failed_count)

@app.get("/stats/processing", response_model=QueueStatsResponse)
async def get_processing_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs currently processing."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    processing_count = job_tracker.get_job_count_by_state("processing")
    return QueueStatsResponse(count=processing_count)

@app.get("/stats/workers", response_model=WorkerInfoResponse)
async def get_worker_info(authorized: bool = Depends(verify_auth_token)):
    """Get information about active workers."""
    # In a real implementation, we would get this from Celery
    # For now, return empty list
    return WorkerInfoResponse(workers=[])

@app.get("/stats/job/{job_uuid}", response_model=JobHistoryResponse)
async def get_job_history(job_uuid: str, authorized: bool = Depends(verify_auth_token)):
    """Get detailed history for a specific job."""
    # In a real implementation, we would get this from job tracking storage
    job = job_tracker.get_job(job_uuid)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Convert datetime objects to strings for JSON serialization
    if "created_at" in job and hasattr(job["created_at"], "isoformat"):
        job["created_at"] = job["created_at"].isoformat()
    if "updated_at" in job and hasattr(job["updated_at"], "isoformat"):
        job["updated_at"] = job["updated_at"].isoformat()
    if "expires_at" in job and hasattr(job["expires_at"], "isoformat"):
        job["expires_at"] = job["expires_at"].isoformat()
    
    return JobHistoryResponse(job=job)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Add datetime import at the top
import datetime

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=config.get("api.host", "0.0.0.0"),
        port=config.get("api.port", 8000),
        reload=config.get("api.reload", True)
    )