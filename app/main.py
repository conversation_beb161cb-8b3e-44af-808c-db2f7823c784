from math import log
from fastapi import FastAP<PERSON>, HTTPException, Head<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uuid
import logging
from app.models import DetectionRequest, DetectionRequestHeaders, DetectionResponse, ErrorResponse, QueueStatsResponse, JobListResponse, WorkerInfoResponse, JobHistoryResponse
from app.config.settings import config
from app.tasks import process_video
from app.celery_app import celery_app
from app.job_tracking import job_tracker

# Initialize FastAPI app
app = FastAPI(
    title="Object Detection Service",
    description="A service for detecting objects, faces, license plates, and barcodes in videos",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def verify_auth_token(authorization: Optional[str] = Header(None)):
    """Verify JWT token if authentication is enabled."""
    auth_enabled = config.get("auth.enabled", False)
    if not auth_enabled:
        return True
    
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    # In a real implementation, we would verify the JWT token here
    # For now, we'll just check if it starts with "Bearer "
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    return True

@app.post("/detect", response_model=DetectionResponse, responses={409: {"model": ErrorResponse}})
async def detect_objects(
    request: DetectionRequest,
    headers: DetectionRequestHeaders = Depends(),
    authorized: bool = Depends(verify_auth_token)
):
    """
    Submit a video for object detection.

    Args:
        request: Detection request containing video URL, callback URL, and detection configuration
        headers: Optional OpenTelemetry headers (fallback if not in request)
        authorized: Authentication dependency

    Returns:
        DetectionResponse: Task information
    """
    # Extract UUID from file info
    job_uuid = request.file.uuid

    # Check if job with UUID already exists
    if job_tracker.job_exists(job_uuid):
        logger.warning(f"Job with UUID {job_uuid} already exists")
        raise HTTPException(status_code=409, detail="job already exists")

    # Use OpenTelemetry info from request, fallback to headers
    ot_traceid = request.ot.trace_id if request.ot else (headers.ot_traceid or str(uuid.uuid4()).replace("-", ""))
    ot_spanid = request.ot.span_id if request.ot else (headers.ot_spanid or str(uuid.uuid4())[:16])

    # Create job record
    if not job_tracker.create_job(job_uuid, ot_traceid, ot_spanid):
        logger.warning(f"Failed to create job with UUID {job_uuid}")
        raise HTTPException(status_code=409, detail="job already exists")

    # Extract configuration settings
    config = request.db.configuration

    # Queue task with Celery - map new structure to existing task parameters
    task_params = {
        "uuid": job_uuid,
        "video_url": request.resources.url,
        "callback_url": request.response.callback,
        # Person detection (maps to object detection)
        "object_detection_enabled": config.person_detection.enabled,
        "object_detection_confidence_threshold": config.global_threshold,
        # Face recognition
        "face_recognition_enabled": config.face_recognition.enabled,
        "face_recognition_confidence_threshold": config.global_threshold,
        # License plate recognition
        "license_plate_recognition_enabled": config.plate_detection.enabled,
        "license_plate_recognition_confidence_threshold": config.global_threshold,
        # Barcode recognition
        "barcode_recognition_enabled": config.barcode_detection.enabled,
        "barcode_recognition_confidence_threshold": config.global_threshold,
        # Image export (default to False for now)
        "detected_image_export": False,
        # OpenTelemetry
        "ot_traceid": ot_traceid,
        "ot_spanid": ot_spanid,
        # Additional configuration data
        "standard_skip_frame": float(config.standard_skip_frame),
        "package_skip_frame": float(config.package_skip_frame),
        "known_people": [person.dict() for person in request.db.elements.known_people],
        "detection_config": config.dict()
    }

    task = process_video.delay(task_params)
    task_id = task.id

    # Update job state
    job_tracker.update_job_state(job_uuid, "queued", task_id)

    logger.info(f"Job {job_uuid} queued with task ID {task_id}")

    return DetectionResponse(
        status="queued",
        task_id=task_id,
        uuid=job_uuid
    )

@app.get("/stats/queue", response_model=QueueStatsResponse)
async def get_queue_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_count = job_tracker.get_job_count_by_state("queued")
    return QueueStatsResponse(count=queued_count)

@app.get("/stats/queue/list", response_model=JobListResponse)
async def get_queue_list(authorized: bool = Depends(verify_auth_token)):
    """Get a list of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_jobs = job_tracker.get_jobs_by_state("queued")
    # Convert datetime objects to strings for JSON serialization
    for job in queued_jobs:
        if "created_at" in job and hasattr(job["created_at"], "isoformat"):
            job["created_at"] = job["created_at"].isoformat()
    return JobListResponse(jobs=queued_jobs)

@app.get("/stats/completed", response_model=QueueStatsResponse)
async def get_completed_stats(authorized: bool = Depends(verify_auth_token)):
    completed_count = job_tracker.get_job_count_by_state("completed")
    return QueueStatsResponse(count=completed_count)

@app.get("/stats/failed", response_model=QueueStatsResponse)
async def get_failed_stats(authorized: bool = Depends(verify_auth_token)):
    failed_count = job_tracker.get_job_count_by_state("failed")    
    return QueueStatsResponse(count=failed_count)

@app.get("/stats/processing", response_model=QueueStatsResponse)
async def get_processing_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs currently processing."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    processing_count = job_tracker.get_job_count_by_state("processing")
    return QueueStatsResponse(count=processing_count)

@app.get("/stats/workers", response_model=WorkerInfoResponse)
async def get_worker_info(authorized: bool = Depends(verify_auth_token)):
    """Get information about active workers."""
    # In a real implementation, we would get this from Celery
    # For now, return empty list
    return WorkerInfoResponse(workers=[])

@app.get("/stats/job/{job_uuid}", response_model=JobHistoryResponse)
async def get_job_history(job_uuid: str, authorized: bool = Depends(verify_auth_token)):
    """Get detailed history for a specific job."""
    # In a real implementation, we would get this from job tracking storage
    job = job_tracker.get_job(job_uuid)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Convert datetime objects to strings for JSON serialization
    if "created_at" in job and hasattr(job["created_at"], "isoformat"):
        job["created_at"] = job["created_at"].isoformat()
    if "updated_at" in job and hasattr(job["updated_at"], "isoformat"):
        job["updated_at"] = job["updated_at"].isoformat()
    if "expires_at" in job and hasattr(job["expires_at"], "isoformat"):
        job["expires_at"] = job["expires_at"].isoformat()
    
    return JobHistoryResponse(job=job)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Add datetime import at the top
import datetime

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=config.get("api.host", "0.0.0.0"),
        port=config.get("api.port", 8000),
        reload=config.get("api.reload", True)
    )