from math import log
from fastapi import FastAP<PERSON>, HTTPException, Head<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uuid
import logging
from app.models import DetectionRequest, DetectionRequestHeaders, DetectionResponse, ErrorResponse, QueueStatsResponse, JobListResponse, WorkerInfoResponse, JobHistoryResponse
from app.config.settings import config
from app.tasks import process_video
from app.celery_app import celery_app
from app.job_tracking import job_tracker

# Initialize FastAPI app
app = FastAPI(
    title="Object Detection Service",
    description="A service for detecting objects, faces, license plates, and barcodes in videos",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def verify_auth_token(authorization: Optional[str] = Header(None)):
    """Verify JWT token if authentication is enabled."""
    auth_enabled = config.get("auth.enabled", False)
    if not auth_enabled:
        return True
    
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    # In a real implementation, we would verify the JWT token here
    # For now, we'll just check if it starts with "Bearer "
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    return True

@app.post("/detect", response_model=DetectionResponse, responses={409: {"model": ErrorResponse}})
async def detect_objects(
    request: DetectionRequest,
    headers: DetectionRequestHeaders = Depends(),
    authorized: bool = Depends(verify_auth_token)
):
    """
    Submit a video for object detection.
    
    Args:
        request: Detection request containing video URL and callback URL
        headers: Optional OpenTelemetry headers
        authorized: Authentication dependency
        
    Returns:
        DetectionResponse: Task information
    """
    # Check if job with UUID already exists
    if job_tracker.job_exists(request.uuid):
        logger.warning(f"Job with UUID {request.uuid} already exists")
        raise HTTPException(status_code=409, detail="job already exists")
    
    # Generate trace/span IDs if not provided
    ot_traceid = headers.ot_traceid or str(uuid.uuid4()).replace("-", "")
    ot_spanid = headers.ot_spanid or str(uuid.uuid4())[:16]
    
    # Create job record
    if not job_tracker.create_job(request.uuid, ot_traceid, ot_spanid):
        logger.warning(f"Failed to create job with UUID {request.uuid}")
        raise HTTPException(status_code=409, detail="job already exists")
    
    # Queue task with Celery
    task_params = {
        "uuid": request.uuid,
        "video_url": request.video_url,
        "callback_url": request.callback_url,
        "object_detection_enabled": request.object_detection_enabled,
        "object_detection_confidence_threshold": request.object_detection_confidence_threshold,
        "face_recognition_enabled": request.face_recognition_enabled,
        "face_recognition_confidence_threshold": request.face_recognition_confidence_threshold,
        "license_plate_recognition_enabled": request.license_plate_recognition_enabled,
        "license_plate_recognition_confidence_threshold": request.license_plate_recognition_confidence_threshold,
        "barcode_recognition_enabled": request.barcode_recognition_enabled,
        "barcode_recognition_confidence_threshold": request.barcode_recognition_confidence_threshold,
        "detected_image_export": request.detected_image_export,
        "ot_traceid": ot_traceid,
        "ot_spanid": ot_spanid
    }
    
    task = process_video.delay(task_params)
    task_id = task.id
    
    # Update job state
    job_tracker.update_job_state(request.uuid, "queued", task_id)
    
    logger.info(f"Job {request.uuid} queued with task ID {task_id}")
    
    return DetectionResponse(
        status="queued",
        task_id=task_id,
        uuid=request.uuid
    )

@app.get("/stats/queue", response_model=QueueStatsResponse)
async def get_queue_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_count = job_tracker.get_job_count_by_state("queued")
    return QueueStatsResponse(count=queued_count)

@app.get("/stats/queue/list", response_model=JobListResponse)
async def get_queue_list(authorized: bool = Depends(verify_auth_token)):
    """Get a list of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_jobs = job_tracker.get_jobs_by_state("queued")
    # Convert datetime objects to strings for JSON serialization
    for job in queued_jobs:
        if "created_at" in job and hasattr(job["created_at"], "isoformat"):
            job["created_at"] = job["created_at"].isoformat()
    return JobListResponse(jobs=queued_jobs)

@app.get("/stats/completed", response_model=QueueStatsResponse)
async def get_completed_stats(authorized: bool = Depends(verify_auth_token)):
    completed_count = job_tracker.get_job_count_by_state("completed")
    return QueueStatsResponse(count=completed_count)

@app.get("/stats/failed", response_model=QueueStatsResponse)
async def get_failed_stats(authorized: bool = Depends(verify_auth_token)):
    failed_count = job_tracker.get_job_count_by_state("failed")    
    return QueueStatsResponse(count=failed_count)

@app.get("/stats/processing", response_model=QueueStatsResponse)
async def get_processing_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs currently processing."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    processing_count = job_tracker.get_job_count_by_state("processing")
    return QueueStatsResponse(count=processing_count)

@app.get("/stats/workers", response_model=WorkerInfoResponse)
async def get_worker_info(authorized: bool = Depends(verify_auth_token)):
    """Get information about active workers."""
    # In a real implementation, we would get this from Celery
    # For now, return empty list
    return WorkerInfoResponse(workers=[])

@app.get("/stats/job/{job_uuid}", response_model=JobHistoryResponse)
async def get_job_history(job_uuid: str, authorized: bool = Depends(verify_auth_token)):
    """Get detailed history for a specific job."""
    # In a real implementation, we would get this from job tracking storage
    job = job_tracker.get_job(job_uuid)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Convert datetime objects to strings for JSON serialization
    if "created_at" in job and hasattr(job["created_at"], "isoformat"):
        job["created_at"] = job["created_at"].isoformat()
    if "updated_at" in job and hasattr(job["updated_at"], "isoformat"):
        job["updated_at"] = job["updated_at"].isoformat()
    if "expires_at" in job and hasattr(job["expires_at"], "isoformat"):
        job["expires_at"] = job["expires_at"].isoformat()
    
    return JobHistoryResponse(job=job)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Add datetime import at the top
import datetime

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=config.get("api.host", "0.0.0.0"),
        port=config.get("api.port", 8000),
        reload=config.get("api.reload", True)
    )