from math import log
from fastapi import <PERSON>AP<PERSON>, HTTPException, Header, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import uuid
import logging
import ast
import jwt
from jwt.exceptions import (
    DecodeError,
    ExpiredSignatureError,
    InvalidTokenError,
    InvalidSignatureError,
    InvalidAudienceError,
    InvalidIssuerError,
    InvalidIssuedAtError,
    ImmatureSignatureError
)
import time
import os, sys, json
from app.models import TaskParameters
from app.models import DetectionRequest, DetectionResponse, ErrorResponse, QueueStatsResponse, JobListResponse, WorkerInfoResponse, JobHistoryResponse
from app.config.settings import config
from app.tasks import process_video
from app.celery_app import celery_app
from app.job_tracking import job_tracker

# Initialize FastAPI app
app = FastAPI(
    title="Object Detection Service",
    description="A service for detecting objects, faces, license plates, and barcodes in videos",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def verify_auth_token(authorization: Optional[str] = Header(None)):
    """Verify JWT token if authentication is enabled."""
    auth_enabled = config.get("auth.enabled", False)
    if not auth_enabled:
        return True
    
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    # In a real implementation, we would verify the JWT token here
    # For now, we'll just check if it starts with "Bearer "
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    
    return True

def get_auth_dependency():
    """Return auth dependency based on config."""
    auth_enabled = config.get("auth.enabled", False)
    if auth_enabled:
        return Depends(verify_auth_token)
    else:
        return None

def find_existing(base_str, values_list):
    base_items = base_str.strip(',').split(',')
    found = [v for v in values_list if v in base_items]
    return ','.join(found)

def decode_jwt(token: str):
    try:
        jwt_secret = config.get("auth.secret_key", "")
        jwt_algorithm = config.get("auth.algorithm", "HS256")

        decoded_token = jwt.decode(token, jwt_secret, algorithms=[jwt_algorithm])
        # return decoded_token if decoded_token["exp"] >= time.time() else None
    except ExpiredSignatureError:
        raise jwt.ExpiredSignatureError("Token has expired")
    except InvalidSignatureError:
        raise jwt.InvalidSignatureError("Invalid token signature")
    except DecodeError:
        raise jwt.DecodeError("Invalid token format")
    except InvalidTokenError:
        raise jwt.InvalidTokenError("Invalid token")

def generate_ids():
    import secrets
    span_id = secrets.token_hex(8).upper()
    trace_id = secrets.token_hex(16).upper()

    return str(span_id), str(trace_id)

def prepare_task_params(payload,auth_enabled=False,authorization=None):
    try:
        resource_method = ""
        resource_url = ""
        rqresponse_callback = ""
        rqfile_filename = ""
        refile_uuid = ""

        hostname = os.uname().nodename

        # ot_trace_id = ""
        # ot_span_id = ""
        ReqOpenTelemetry = payload.ot
        if ReqOpenTelemetry is not None:
            ot_span_id = ReqOpenTelemetry.span_id
            ot_trace_id = ReqOpenTelemetry.trace_id
        else:
            ot_span_id, ot_trace_id = generate_ids()

        ReqResources = payload.resources
        if ReqResources is not None:
            resource_method = ReqResources.method
            resource_url = ReqResources.url

        rqfile_meta = None
        rqfile_meta_controller_uuid = ""
        rqfile_meta_device_id = ""
        ReqFile = payload.file
        if ReqFile is not None:
            refile_uuid = ReqFile.uuid
            # rqfile_timestamp = ReqFile.timestamp
            # rqfile_size = ReqFile.size
            # rqfile_format = ReqFile.file_format
            rqfile_filename = ReqFile.filename
            rqfile_meta = ReqFile.meta

        if rqfile_meta is None or rqfile_meta == "":
            logger.debug(f"meta is not json format uuid:{refile_uuid}")
        else:
            try:
                rqfile_meta_json = json.loads(rqfile_meta)
                rqfile_meta_controller_info = rqfile_meta_json.get("controller_info",{})
                rqfile_meta_controller_uuid = rqfile_meta_controller_info.get("controller_uuid","")
                rqfile_meta_device_id = rqfile_meta_controller_info.get("device_id", "")
            except json.JSONDecodeError as e:
                logger.debug(f"meta is not json format uuid:{refile_uuid}")

        if auth_enabled:
            token = authorization.split(" ")[1]
            try:
                decoded_token = decode_jwt(token)
            except jwt.ExpiredSignatureError:
                raise jwt.ExpiredSignatureError("Token has expired")
            except jwt.InvalidSignatureError:
                raise jwt.InvalidSignatureError("Invalid token signature")
            except jwt.DecodeError:
                raise jwt.DecodeError("Invalid token format")
            except jwt.InvalidTokenError:
                raise jwt.InvalidTokenError("Invalid token")

        plate_detection = False
        plate_detection_filter_items = None
        person_detection = False
        package_detection = False
        animal_detection = False
        animal_detection_items = None
        face_recognition = False
        face_recognition_filter_items = None
        barcode_detection = False
        barcode_detection_filter_items = None
        vehicle_detection = False
        vehicle_detection_items = None
        hotzone_detection = False
        hotzone_zones = []
        face_recognition_known_people = []
        global_confidence_threshold = config.get("yolo.confidence_threshold", 0.65)
        standard_skip_frame = "0"
        package_skip_frame = "0"

        reqDb = payload.db
        if reqDb is not None:
            rqdb_elements = reqDb.elements
            rqdb_elements_configuration = reqDb.configuration

            if "standard_skip_frame" in rqdb_elements_configuration:
                standard_skip_frame = rqdb_elements_configuration.standard_skip_frame

            if "package_skip_frame" in rqdb_elements_configuration:
                package_skip_frame = rqdb_elements_configuration.package_skip_frame

            if "hotzones" in rqdb_elements_configuration:
                rqdb_elements_configuration_hotzone_detection = rqdb_elements_configuration.hotzones
                hotzone_detection = rqdb_elements_configuration_hotzone_detection.enabled
                hotzone_zones = rqdb_elements_configuration_hotzone_detection.zones

            if "global_threshold" in rqdb_elements_configuration:
                global_confidence_threshold = rqdb_elements_configuration.global_threshold

            if "person_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_person_detection = rqdb_elements_configuration.person_detection
                person_detection = rqdb_elements_configuration_person_detection.enabled

            if "package_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_package_detection = rqdb_elements_configuration.package_detection
                package_detection = rqdb_elements_configuration_package_detection.enabled

            if "animal_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_animal_detection = rqdb_elements_configuration.animal_detection
                animal_detection = rqdb_elements_configuration_animal_detection.enabled
                if "items" in rqdb_elements_configuration_animal_detection:
                    animal_detection_items = rqdb_elements_configuration_animal_detection.items

            if "vehicle_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_vehicle_detection = rqdb_elements_configuration.vehicle_detection
                vehicle_detection = rqdb_elements_configuration_vehicle_detection.enabled
                if "items" in rqdb_elements_configuration_vehicle_detection:
                    vehicle_detection_items = rqdb_elements_configuration_vehicle_detection.items

            if "face_recognition" in rqdb_elements_configuration:
                rqdb_elements_configuration_face_recognition = rqdb_elements_configuration.face_recognition
                face_recognition = rqdb_elements_configuration_face_recognition.enabled
                if "items" in rqdb_elements_configuration_face_recognition:
                    face_recognition_filter_items = rqdb_elements_configuration_face_recognition.items
                    face_recognition_filter_items = None # not supported now

            # support for barcode and person detection has been disabled as support is not yet available.

            if "known_people" in rqdb_elements:
                face_recognition_known_people = rqdb_elements.known_people

            if "barcode_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_barcode_detection = rqdb_elements_configuration.barcode_detection
                barcode_detection = rqdb_elements_configuration_barcode_detection.enabled
                barcode_detection = False # not supported now
                if "items" in rqdb_elements_configuration_barcode_detection:
                    barcode_detection_filter_items = rqdb_elements_configuration_barcode_detection.items
                    barcode_detection_filter_items = None # not supported now

            if "plate_detection" in rqdb_elements_configuration:
                rqdb_elements_configuration_plate_detection = rqdb_elements_configuration.plate_detection
                plate_detection = rqdb_elements_configuration_plate_detection.enabled
                plate_detection = False  # not supported now
                if "items" in rqdb_elements_configuration_plate_detection:
                    plate_detection_filter_items = rqdb_elements_configuration_plate_detection.items
                    plate_detection_filter_items = None # not supported now

        reqResponse = payload.response
        if reqResponse is not None:
            rqresponse_callback = reqResponse.callback

        step00_downloadFile_savedFileDir = config.get("file_processing.temp_directory", "/tmp")

        step30_standartDetection_confidenceThreshold = global_confidence_threshold

        step30_ml_device = config.get("yolo.device", "gpu")
        step30_licensePlate_confidenceThreshold = float(config.get("license_plate_recognition.confidence_threshold", 0.69))
        step30_licensePlate_country = config.get("license_plate_recognition.country", "us")
        step30_standardDetection_model = config.get("yolo.model", "ezlomodels/ezlo-yolo-standard-11s.pt")
        step30_packageDetection_model = config.get("package.model", "ezlomodels/ezlo-yolo-package.pt")
        step30_customPackageDetection_model = config.get("custom_package.model", "ezlomodels/ezlo-custom-package.pt")
        step30_lplateDetection_model = config.get("license_plate_recognition.model", "ezlomodels/license_plate_detector.pt")

        person_classes = config.get("yolo.person_detection_classes", "person,")
        package_classes = config.get("package.detection_classes", "parcel,box,bundle,packet,package,crate,carton,envelope,case,kit,bag,")

        licenseplate_classes = config.get("license_plate_recognition.detection_classes", "license plate,")

        animal_classes = config.get("yolo.animal_detection_classes", "cat,dog,")
        vehicle_classes = config.get("yolo.vehicle_detection_classes", "car,motorcycle,")

        if standard_skip_frame == "0":
            standard_skip_frame = config.get("yolo.skip_frame_ratio", "0.5")

        if package_skip_frame == "0":
            package_skip_frame = config.get("package.skip_frame_ratio", "0.5")

        if animal_detection_items is not None:
            if isinstance(animal_detection_items, list):
                if len(animal_detection_items) > 0:
                    animal_classes = find_existing(animal_classes, animal_detection_items)
                    if animal_classes != "":
                        animal_detection = True
                    else:
                        animal_detection = False
                else:
                    animal_detection = False
                    animal_classes = ""

        if vehicle_detection_items is not None:
            if isinstance(vehicle_detection_items, list):
                if len(vehicle_detection_items) > 0:
                    vehicle_classes = find_existing(vehicle_classes, vehicle_detection_items)
                    if vehicle_classes != "":
                        vehicle_detection = True
                    else:
                        vehicle_detection = False
                else:
                    vehicle_detection = False
                    vehicle_classes = ""

        if isinstance(face_recognition_filter_items, list):
            if len(face_recognition_filter_items) > 0:
                face_recognition = True
            else:
                face_recognition = False
        else:
            face_recognition_filter_items = None

        if isinstance(barcode_detection_filter_items, list):
            if len(barcode_detection_filter_items) > 0:
                barcode_detection = True
            else:
                barcode_detection = False
        else:
            barcode_detection_filter_items = None

        payload = {
            "uuid": refile_uuid,
            "response_callback": rqresponse_callback,
            "log_debug": False,
            "step10_downloadFile": {
                "filename": rqfile_filename,
                "resource_method": resource_method,
                "resource_url": resource_url,
                "configuration": {
                    "enabled": True,
                    "savedFileDir": step00_downloadFile_savedFileDir
                }
            },
            "step20_fileValidate": {
                "mp4Check": False,
                "decompress": False,
                "configuration": {
                    "enabled": False
                }
            },
            "step30_objectDetection": {
                "ml_device": step30_ml_device,
                "face_recognition": face_recognition,
                "face_recognition_filter_items": face_recognition_filter_items,
                "face_recognition_known_people": face_recognition_known_people,
                "plate_detection": plate_detection,
                "plate_detection_filter_items": plate_detection_filter_items,
                "person_detection": person_detection,
                "animal_detection": animal_detection,
                "vehicle_detection": vehicle_detection,
                "package_detection": package_detection,
                "barcode_detection": barcode_detection,
                "barcode_detection_filter_items": barcode_detection_filter_items,
                "hotzone_detection": hotzone_detection,
                "person_classes": person_classes,
                "vehicle_classes": vehicle_classes,
                "animal_classes": animal_classes,
                "package_classes": package_classes,
                "licenseplate_classes": licenseplate_classes,
                "configuration": {
                    "enabled": True,
                    "standard_skip_frame": standard_skip_frame,
                    "package_skip_frame": package_skip_frame,
                    "standard_model": step30_standardDetection_model,
                    "package_model": step30_packageDetection_model,
                    "custom_package_model": step30_customPackageDetection_model,
                    "lplate_model": step30_lplateDetection_model,
                    "confidence_threshold": step30_standartDetection_confidenceThreshold,
                    "licensePlateConfidenceThreshold": step30_licensePlate_confidenceThreshold,
                    "licensePlateCountry": step30_licensePlate_country
                },
                "hotzone_zones": hotzone_zones
            },
            "step40_sendResult": {
                "configuration": {
                    "enabled": True
                }
            },
            "step50_cleanEnvironment": {
                "deleteLocalFile": True,
                "configuration": {
                    "enabled": True
                }
            },
            "ot_trace_id": ot_trace_id,
            "ot_span_id": ot_span_id,
            "controller_uuid": rqfile_meta_controller_uuid,
            "device_id": rqfile_meta_device_id,
            "hostname": hostname
        }

        return payload
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"Error in main_helper.prepare_payload: {exc_type} file:{fname} line:{exc_tb.tb_lineno} {e}")
        return None

@app.post("/detect", response_model=DetectionResponse, responses={409: {"model": ErrorResponse}})
async def detect_objects(
    request: DetectionRequest,
    authorization: Optional[str] = Header(None)
):
    """
    Submit a video for object detection.

    Args:
        request: Detection request containing video URL, callback URL, and detection configuration
        authorization: Optional JWT token for authentication (required if auth.enabled is true)

    Returns:
        DetectionResponse: Task information
    """
    # Extract UUID from file info
    job_uuid = request.file.uuid

    # Check authentication if enabled
    auth_enabled = config.get("auth.enabled", False)
    if auth_enabled:
        if not authorization:
            return DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="authorization header missing"
            )

        if not authorization.startswith("Bearer "):
            return DetectionResponse(
                status="failed",
                status_code=401,
                task_id="",
                uuid=job_uuid,
                message="invalid authorization header"
            )

    # Check if job with UUID already exists
    if job_tracker.job_exists(job_uuid):
        logger.warning(f"Job with UUID {job_uuid} already exists")
        return DetectionResponse(
            status="failed",
            status_code=409,
            task_id="",
            uuid=job_uuid,
            message="job already exists"
        )

    # Prepare task parameters
    # task_params = None
    # try:
    #     task_params = prepare_task_params(request,auth_enabled,authorization)
    # except Exception as e:
    #     logger.info(f">>>>>>>>>>>>>>>Error in prepare_task_params: {e}")
    task_params = prepare_task_params(request, auth_enabled, authorization)

    if task_params is None:
        return DetectionResponse(
            status="failed",
            status_code=500,
            task_id="",
            uuid=job_uuid,
            message="internal server error"
        )

    logger.info(f"Task parameters prepared {task_params}")

    # Create job record
    ot_trace_id = request.ot.trace_id
    ot_span_id = request.ot.span_id
    job_tracker.create_job(job_uuid, ot_trace_id, ot_span_id)

    # Queue task
    task = process_video.delay(task_params)
    task_id = task.id

    # Update job state
    job_tracker.update_job_state(job_uuid, "main", "queued", task_id)

    return DetectionResponse(
        status="queued",
        status_code=200,
        task_id=task_id,
        uuid=job_uuid,
        message="job added successfully"
    )

@app.get("/stats/queue", response_model=QueueStatsResponse)
async def get_queue_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_count = job_tracker.get_job_count_by_state("queued")
    return QueueStatsResponse(count=queued_count)

@app.get("/stats/queue/list", response_model=JobListResponse)
async def get_queue_list(authorized: bool = Depends(verify_auth_token)):
    """Get a list of jobs in the queue."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_jobs = job_tracker.get_jobs_by_state("queued")
    # Convert datetime objects to strings for JSON serialization
    for job in queued_jobs:
        if "created_at" in job and hasattr(job["created_at"], "isoformat"):
            job["created_at"] = job["created_at"].isoformat()
    return JobListResponse(jobs=queued_jobs)

@app.get("/stats/completed", response_model=QueueStatsResponse)
async def get_completed_stats(authorized: bool = Depends(verify_auth_token)):
    completed_count = job_tracker.get_job_count_by_state("completed")
    return QueueStatsResponse(count=completed_count)

@app.get("/stats/failed", response_model=QueueStatsResponse)
async def get_failed_stats(authorized: bool = Depends(verify_auth_token)):
    failed_count = job_tracker.get_job_count_by_state("failed")    
    return QueueStatsResponse(count=failed_count)

@app.get("/stats/processing", response_model=QueueStatsResponse)
async def get_processing_stats(authorized: bool = Depends(verify_auth_token)):
    """Get the number of jobs currently processing."""
    # In a real implementation, we would get this from Celery/RabbitMQ
    processing_count = job_tracker.get_job_count_by_state("processing")
    return QueueStatsResponse(count=processing_count)

@app.get("/stats/workers", response_model=WorkerInfoResponse)
async def get_worker_info(authorized: bool = Depends(verify_auth_token)):
    """Get information about active workers."""
    # In a real implementation, we would get this from Celery
    # For now, return empty list
    return WorkerInfoResponse(workers=[])

@app.get("/stats/job/{job_uuid}", response_model=JobHistoryResponse)
async def get_job_history(job_uuid: str, authorized: bool = Depends(verify_auth_token)):
    """Get detailed history for a specific job."""
    # In a real implementation, we would get this from job tracking storage
    job = job_tracker.get_job(job_uuid)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    # Convert datetime objects to strings for JSON serialization
    if "created_at" in job and hasattr(job["created_at"], "isoformat"):
        job["created_at"] = job["created_at"].isoformat()
    if "updated_at" in job and hasattr(job["updated_at"], "isoformat"):
        job["updated_at"] = job["updated_at"].isoformat()
    if "expires_at" in job and hasattr(job["expires_at"], "isoformat"):
        job["expires_at"] = job["expires_at"].isoformat()
    
    return JobHistoryResponse(job=job)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Add datetime import at the top
import datetime

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=config.get("api.host", "0.0.0.0"),
        port=config.get("api.port", 8000),
        reload=config.get("api.reload", True)
    )