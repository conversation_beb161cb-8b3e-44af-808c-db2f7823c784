from datetime import datetime, timedelta
from typing import List, Optional

from sqlalchemy.exc import SQLAlchemyError

from app.config.logger_factory import get_logger
from app.config.settings import config
from app.database import BaseRepository, Job, db_manager

logger = get_logger(__name__)


class JobRepository(BaseRepository):
    def create_job(self, uuid: str, ot_traceid: str, ot_spanid: str, retention_days: Optional[int] = None) -> bool:
        try:
            # Check if job already exists using read-only session
            if self.job_exists(uuid):
                logger.warning(f"Job with UUID {uuid} already exists")
                return False

            retention_days = retention_days or config.get("job_tracking.retention_days", 7)
            expires_at = datetime.utcnow() + timedelta(days=retention_days)

            # Use write session for creating the job
            with self.get_session() as session:
                job = Job(
                    uuid=uuid,
                    state="queued",
                    task_id=None,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    expires_at=expires_at,
                    ot_traceid=ot_traceid,
                    ot_spanid=ot_spanid,
                )
                session.add(job)
                # Automatic commit happens here due to context manager

            logger.info(f"Job {uuid} created successfully")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Database error creating job {uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error creating job {uuid}: {str(e)}")
            return False

    def job_exists(self, uuid: str) -> bool:
        try:
            with self.get_read_session() as session:
                job = session.query(Job).filter(Job.uuid == uuid).first()
                return job is not None
        except SQLAlchemyError as e:
            logger.error(f"Database error checking job existence for {uuid}: {str(e)}")
            return True  # Return True to prevent duplicate creation on error
        except Exception as e:
            logger.error(f"Unexpected error checking job existence for {uuid}: {str(e)}")
            return True

    def get_job_by_uuid(self, uuid: str) -> Optional[Job]:
        try:
            with self.get_read_session() as session:
                return session.query(Job).filter(Job.uuid == uuid).first()
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving job {uuid}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving job {uuid}: {str(e)}")
            return None

    def update_job_state(
        self,
        uuid: str,
        step: str,
        state: str,
        error_message: Optional[str] = None,
        task_id: Optional[str] = None,
    ) -> bool:
        try:
            with self.get_session() as session:
                job = session.query(Job).filter(Job.uuid == uuid).first()
                if not job:
                    logger.warning(f"Job {uuid} not found for state update")
                    return False

                job.state = state
                job.step = step
                job.error_message = error_message
                job.updated_at = datetime.utcnow()
                if task_id:
                    job.task_id = task_id
                # Automatic commit happens here

            logger.info(f"Job {uuid} state updated: {step.upper()}/{state.upper()}")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Database error updating job state for {uuid}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating job state for {uuid}: {str(e)}")
            return False

    def get_jobs_by_state(self, state: str, limit: Optional[int] = None) -> List[Job]:
        try:
            with self.get_read_session() as session:
                query = session.query(Job).filter(Job.state == state)
                if limit:
                    query = query.limit(limit)
                return query.all()
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving jobs with state {state}: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving jobs with state {state}: {str(e)}")
            return []

    def cleanup_expired_jobs(self) -> int:
        try:
            with self.get_session() as session:
                current_time = datetime.utcnow()
                expired_jobs = session.query(Job).filter(Job.expires_at < current_time).all()
                count = len(expired_jobs)

                for job in expired_jobs:
                    session.delete(job)
                # Automatic commit happens here

                logger.info(f"Cleaned up {count} expired jobs")
                return count

        except SQLAlchemyError as e:
            logger.error(f"Database error cleaning up expired jobs: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error cleaning up expired jobs: {str(e)}")
            return 0

    def get_job_count_by_state(self, state: str) -> int:
        try:
            with self.get_read_session() as session:
                return session.query(Job).filter(Job.state == state).count()
        except SQLAlchemyError as e:
            logger.error(f"Database error counting jobs with state {state}: {str(e)}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error counting jobs with state {state}: {str(e)}")
            return 0


# Create a singleton instance for use throughout the application
job_repository = JobRepository(db_manager)
