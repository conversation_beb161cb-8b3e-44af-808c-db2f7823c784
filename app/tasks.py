from celery import current_task
from app.celery_app import celery_app
from app.models import TaskParameters, AggregatedDetectionResult
from app.config.settings import config
from app.detection import detect_objects
from app.face_recognition import recognize_faces
from app.license_plate import recognize_license_plates
from app.barcode import recognize_barcodes
from app.image_export import export_detected_images
from app.aggregation import aggregate_results
from app.job_tracking import job_tracker
import logging
import uuid
import requests
import traceback
from datetime import datetime
import os, sys
import tempfile
import magic
import urllib.request
import urllib.error
from urllib.parse import urlparse

import app.step10_downloadfile as step10

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def process_video(self, task_params_dict: dict) -> dict:
    print("!!!!!!!!!!!!!!!")
    try:
        task_params = TaskParameters(**task_params_dict)
    except Exception as e:
        error_msg = f"Failed to prepare task parameters: {str(e)}"
        logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
        
        # Update job state to failed
        job_tracker.update_job_state(task_params.uuid, "failed")
        
        # Send error to callback URL
        send_results_to_callback(task_params, None, "failed", error_msg)

    
    # Update job state to processing
    job_tracker.update_job_state(task_params.uuid, "tasks", "processing", self.request.id)
    
    # Get temp directory from configuration or use default
    temp_base_dir = config.get("file_processing.temp_directory", "/tmp/object-detection")
    # Create task-specific temporary directory
    temp_dir = os.path.join(temp_base_dir, f"object_detection_{task_params.uuid}")
    video_path = os.path.join(temp_base_dir, f"{task_params.uuid}.video.mp4")
    
    try:
        # Download video with error handling
        try:
            step10.downloadfile_run(task_params.uuid, task_params.step10_downloadFile.resource_url, video_path)
        except Exception as e:
            error_msg = f"Failed to download video from {task_params.step10_downloadFile.resource_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "downloafile", "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise

        ############################################################
        """"
        # Validate video file
        try:
            validate_video_file(video_path)
        except Exception as e:
            error_msg = f"Video validation failed for {task_params.step10_downloadFile.resource_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        
        # Process video
        try:
            detection_result = process_video_content(
                video_path, task_params
            )
        except Exception as e:
            error_msg = f"Video processing failed for {task_params.video_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        
        # Export images if requested
        image_urls = {}
        if task_params.detected_image_export and config.get("image_export.enabled", True):
            try:
                image_urls = export_detected_images(video_path, detection_result, task_params.uuid)
            except Exception as e:
                error_msg = f"Failed to export images for UUID {task_params.uuid}: {str(e)}"
                logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
                
                # Update job state to failed
                job_tracker.update_job_state(task_params.uuid, "failed")
                
                # Send error to callback URL
                send_results_to_callback(task_params, None, "failed", error_msg)
                
                # Re-raise the exception so Celery can handle retries
                raise
        
        # Aggregate results
        try:
            aggregated_result = aggregate_results(
                detection_result, 
                image_urls, 
                task_params.ot_traceid, 
                task_params.ot_spanid
            )
        except Exception as e:
            error_msg = f"Failed to aggregate results for UUID {task_params.uuid}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        
        # Send results to callback URL
        try:
            send_results_to_callback(task_params, aggregated_result, "success", None)
        except Exception as e:
            error_msg = f"Failed to send results to callback URL for UUID {task_params.uuid}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        """

        # Update job state to completed
        job_tracker.update_job_state(task_params.uuid, "tasks","completed")

        return {"status": "success", "uuid": task_params.uuid}
        
    except Exception as e:
        logger.error(f"Error processing video for UUID {task_params.uuid}: {str(e)}")
        logger.error(traceback.format_exc())
        
        # Update job state to failed (if not already updated)
        job_tracker.update_job_state(task_params.uuid, "failed")
        
        # Note: Not sending error to callback here since it should have been sent in the specific error handlers above
        
        # Re-raise the exception so Celery can handle retries
        raise
        
    finally:
        # Clean up temporary files
        try:
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up temporary directory for UUID: {task_params.uuid}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory for UUID {task_params.uuid}: {str(e)}")

def validate_video_file(video_path: str):
    """
    Validate that the file is an MP4 video.
    
    Args:
        video_path: Path to the video file
    """
    if not config.get("file_validation.enabled", True):
        return
    
    logger.info(f"Validating video file: {video_path}")
    
    # Check if file exists
    if not os.path.exists(video_path):
        raise ValueError("Video file does not exist")
    
    # Check file size
    max_size_mb = config.get("file_validation.max_file_size_mb", 1024)
    file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
    if file_size_mb > max_size_mb:
        raise ValueError(f"Video file too large: {file_size_mb:.2f} MB (max {max_size_mb} MB)")
    
    # Check file type using magic
    allowed_types = config.get("file_validation.allowed_content_types", ["video/mp4"])
    try:
        file_type = magic.from_file(video_path, mime=True)
    except Exception as e:
        raise ValueError(f"Failed to determine file type: {str(e)}")
    
    if file_type not in allowed_types:
        raise ValueError(f"Invalid file type: {file_type} (allowed: {allowed_types})")
    
    # Check file extension
    allowed_extensions = config.get("file_validation.allowed_extensions", [".mp4"])
    file_extension = os.path.splitext(video_path)[1].lower()
    if file_extension not in allowed_extensions:
        raise ValueError(f"Invalid file extension: {file_extension} (allowed: {allowed_extensions})")
    
    logger.info(f"Video file validation passed: {file_type}, {file_size_mb:.2f} MB")

def process_video_content(video_path: str, task_params: TaskParameters) -> dict:
    """
    Process video content with all enabled detection methods.
    
    Args:
        video_path: Path to the video file
        task_params: Task parameters
        
    Returns:
        dict: Detection results
    """
    logger.info(f"Processing video content for UUID: {task_params.uuid}")
    
    # Initialize result dictionary
    result = {
        "uuid": task_params.uuid,
        "timestamp": datetime.utcnow().isoformat(),
        "video_url": task_params.video_url,
        "objects": [],
        "faces": [],
        "license_plates": [],
        "barcodes": []
    }
    
    # Object detection
    if task_params.object_detection_enabled and config.get("yolo.enabled", True):
        try:
            logger.info(f"Performing object detection for UUID: {task_params.uuid}")
            logger.info(f"Performing object detection for video_path: {video_path}")
            detection_classes = config.get("yolo.detection_classes")
            logger.info(f"Performing object detection for detection_classes: {detection_classes}")
            logger.info(f"Performing object detection for confidence_threshold: {task_params.object_detection_confidence_threshold}")
            device = config.get("yolo.device", "cpu")
            logger.info(f"Performing object detection for device: {device}")
            objects = detect_objects(
                video_path,
                detection_classes=config.get("yolo.detection_classes"),
                confidence_threshold=task_params.object_detection_confidence_threshold,
                device=config.get("yolo.device", "cpu")
            )
            result["objects"] = objects
            logger.info(f"Object detection completed. Found {len(objects)} objects")
        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            error_msg = f"Object detection failed for UUID {task_params.uuid}: {str(e)} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
            logger.error(error_msg)
            # We don't raise here because we want to continue with other detection methods
            # The error is logged for debugging purposes
    
    # Face recognition
    if task_params.face_recognition_enabled and config.get("face_recognition.enabled", True):
        try:
            logger.info(f"Performing face recognition for UUID: {task_params.uuid}")
            faces = recognize_faces(
                video_path,
                confidence_threshold=task_params.face_recognition_confidence_threshold
            )
            result["faces"] = faces
            logger.info(f"Face recognition completed. Found {len(faces)} faces")
        except Exception as e:
            error_msg = f"Face recognition failed for UUID {task_params.uuid}: {str(e)}"
            logger.error(error_msg)
            # We don't raise here because we want to continue with other detection methods
            # The error is logged for debugging purposes
    
    # License plate recognition
    if task_params.license_plate_recognition_enabled and config.get("license_plate_recognition.enabled", True):
        try:
            logger.info(f"Performing license plate recognition for UUID: {task_params.uuid}")
            plates = recognize_license_plates(
                video_path,
                confidence_threshold=task_params.license_plate_recognition_confidence_threshold
            )
            result["license_plates"] = plates
            logger.info(f"License plate recognition completed. Found {len(plates)} plates")
        except Exception as e:
            error_msg = f"License plate recognition failed for UUID {task_params.uuid}: {str(e)}"
            logger.error(error_msg)
            # We don't raise here because we want to continue with other detection methods
            # The error is logged for debugging purposes
    
    # Barcode recognition
    if task_params.barcode_recognition_enabled and config.get("barcode_recognition.enabled", True):
        try:
            logger.info(f"Performing barcode recognition for UUID: {task_params.uuid}")
            barcodes = recognize_barcodes(
                video_path,
                confidence_threshold=task_params.barcode_recognition_confidence_threshold
            )
            result["barcodes"] = barcodes
            logger.info(f"Barcode recognition completed. Found {len(barcodes)} barcodes")
        except Exception as e:
            error_msg = f"Barcode recognition failed for UUID {task_params.uuid}: {str(e)}"
            logger.error(error_msg)
            # We don't raise here because we want to continue with other detection methods
            # The error is logged for debugging purposes
    
    return result

def send_results_to_callback(task_params: TaskParameters, result: AggregatedDetectionResult, status: str, error: str = None):
    """
    Send processing results to the callback URL.
    
    Args:
        task_params: Task parameters
        result: Processing result
        status: Processing status ("success" or "failed")
        error: Error message if status is "failed"
    """
    try:
        # Prepare callback payload
        payload = {
            "uuid": task_params.uuid,
            "status": status,
            "results": result.dict() if result else None,
            "error": error,
            "ot_traceid": task_params.ot_traceid,
            "ot_spanid": task_params.ot_spanid
        }
        
        # Send POST request to callback URL
        logger.info(f"Sending results to callback URL for UUID: {task_params.uuid}")
        response = requests.post(task_params.response_callback, json=payload, timeout=30)
        response.raise_for_status()
        
        logger.info(f"Successfully sent results to callback URL for UUID: {task_params.uuid}")
        
    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to send results to callback URL: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error while sending results to callback URL: {str(e)}")