from typing import List, Dict, Any
from app.config.settings import config
import logging
import cv2
import numpy as np
import easyocr
import re

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

class LicensePlateRecognizer:
    """License plate recognizer using EasyOCR."""
    
    def __init__(self):
        """Initialize the license plate recognizer."""
        self.reader = None
        self.confidence_threshold = None
        self.whitelist_characters = None
        self.preprocess_enabled = None
        self.preprocess_options = None
        self.process_every_n_frames = None
        
    def load_model(self, languages: List[str] = None, confidence_threshold: float = None,
                   whitelist_characters: str = None, preprocess_enabled: bool = None,
                   preprocess_options: Dict[str, bool] = None, process_every_n_frames: int = None):
        """
        Load the OCR model.
        
        Args:
            languages: List of languages for OCR
            confidence_threshold: Minimum confidence threshold for detections
            whitelist_characters: Characters to whitelist in license plate recognition
            preprocess_enabled: Whether to preprocess images
            preprocess_options: Preprocessing options
            process_every_n_frames: Process every Nth frame
        """
        # Use configuration values if not provided
        languages = languages or config.get("license_plate_recognition.languages", ["en"])
        self.confidence_threshold = confidence_threshold or config.get("license_plate_recognition.confidence_threshold", 0.5)
        self.whitelist_characters = whitelist_characters or config.get("license_plate_recognition.whitelist_characters", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ")
        self.preprocess_enabled = preprocess_enabled or config.get("license_plate_recognition.preprocess_enabled", True)
        self.preprocess_options = preprocess_options or config.get("license_plate_recognition.preprocess_options", {
            "grayscale": True,
            "blur": False,
            "threshold": True
        })
        self.process_every_n_frames = process_every_n_frames or config.get("license_plate_recognition.process_every_n_frames", 15)
        
        try:
            logger.info(f"Loading EasyOCR with languages: {languages}")
            self.reader = easyocr.Reader(languages)
            logger.info("EasyOCR loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load EasyOCR: {str(e)}")
            raise
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better OCR results.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        if not self.preprocess_enabled:
            return image
        
        # Convert to grayscale
        if self.preprocess_options.get("grayscale", True):
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply blur
        if self.preprocess_options.get("blur", False):
            image = cv2.GaussianBlur(image, (5, 5), 0)
        
        # Apply threshold
        if self.preprocess_options.get("threshold", True):
            _, image = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return image
    
    def filter_license_plate_text(self, text: str) -> str:
        """
        Filter license plate text to only include whitelisted characters.
        
        Args:
            text: Input text
            
        Returns:
            Filtered text
        """
        if not self.whitelist_characters:
            return text
        
        # Create regex pattern from whitelist characters
        pattern = f"[^{re.escape(self.whitelist_characters)}]"
        filtered_text = re.sub(pattern, "", text)
        return filtered_text
    
    def detect_license_plates(self, video_path: str) -> List[Dict[str, Any]]:
        """
        Detect and recognize license plates in a video.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of recognized license plates with timestamps
        """
        if self.reader is None:
            raise ValueError("OCR model not loaded. Call load_model() first.")
        
        logger.info(f"Starting license plate recognition on video: {video_path}")
        
        # Open video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Failed to open video file: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"Video properties - FPS: {fps}, Total frames: {total_frames}")
        
        recognized_plates = []
        frame_count = 0
        
        try:
            while True:
                # Read frame
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process every Nth frame
                if frame_count % self.process_every_n_frames != 0:
                    frame_count += 1
                    continue
                
                # Calculate timestamp
                timestamp = frame_count / fps
                
                # Preprocess frame for license plate detection
                # In a real implementation, we would use a license plate detector here
                # For now, we'll just process the entire frame
                processed_frame = self.preprocess_image(frame)
                
                # Recognize text in frame
                results = self.reader.readtext(processed_frame)
                
                # Process results
                for (bbox, text, confidence) in results:
                    if confidence >= self.confidence_threshold:
                        # Filter text to only include license plate characters
                        filtered_text = self.filter_license_plate_text(text)
                        
                        # Only add if we have some characters after filtering
                        if filtered_text:
                            # Convert bbox to standard format
                            points = np.array(bbox, dtype=np.float32)
                            x, y, w, h = cv2.boundingRect(points)
                            
                            # Add to recognized plates
                            recognized_plates.append({
                                "text": filtered_text,
                                "confidence": confidence,
                                "bounding_box": {
                                    "x": float(x),
                                    "y": float(y),
                                    "width": float(w),
                                    "height": float(h)
                                },
                                "frame_number": frame_count,
                                "timestamp": timestamp
                            })
                
                frame_count += 1
                
                # Log progress
                if frame_count % 100 == 0:
                    logger.info(f"Processed {frame_count}/{total_frames} frames")
        
        finally:
            # Release video capture
            cap.release()
        
        logger.info(f"License plate recognition completed. Found {len(recognized_plates)} plates")
        return recognized_plates

# Global license plate recognizer instance
license_plate_recognizer = LicensePlateRecognizer()

def recognize_license_plates(video_path: str, confidence_threshold: float = None) -> List[Dict[str, Any]]:
    """
    Recognize license plates in a video.
    
    Args:
        video_path: Path to the video file
        confidence_threshold: Minimum confidence threshold for detections
        
    Returns:
        List of recognized license plates with timestamps
    """
    # Load model if not already loaded
    if license_plate_recognizer.reader is None:
        license_plate_recognizer.load_model(confidence_threshold=confidence_threshold)
    
    # Detect license plates
    return license_plate_recognizer.detect_license_plates(video_path)