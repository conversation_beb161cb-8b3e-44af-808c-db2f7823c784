from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from app.database import db_manager, Job
from app.config.settings import config
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

class JobTracker:
    """Job tracking system using PostgreSQL."""

    def __init__(self):
        """Initialize job tracker with PostgreSQL connection."""
        self.storage_type = config.get("job_tracking.storage_type", "postgresql")

        if self.storage_type == "postgresql":
            self._init_postgresql()
        else:
            # In-memory storage for development
            self.jobs = {}

    def _init_postgresql(self):
        """Initialize PostgreSQL connection."""
        try:
            # Database manager is already initialized in database.py
            logger.info("PostgreSQL job tracker initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL job tracker: {str(e)}")
            raise
    
    def job_exists(self, uuid: str) -> bool:
        """
        Check if a job with the given UUID already exists.

        Args:
            uuid: Job UUID

        Returns:
            bool: True if job exists, False otherwise
        """
        try:
            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    job = db.query(Job).filter(Job.uuid == uuid).first()
                    return job is not None
            else:
                return uuid in self.jobs
        except SQLAlchemyError as e:
            logger.error(f"Error checking job existence for UUID {uuid}: {str(e)}")
            # In case of database error, assume job exists to prevent duplicates
            return True
    
    def create_job(self, uuid: str, ot_traceid: str, ot_spanid: str) -> bool:
        """
        Create a new job record.

        Args:
            uuid: Job UUID
            ot_traceid: OpenTelemetry trace ID
            ot_spanid: OpenTelemetry span ID

        Returns:
            bool: True if job was created, False if job already exists
        """
        try:
            # Check if job already exists
            if self.job_exists(uuid):
                return False

            # Calculate expiration time
            retention_days = config.get("job_tracking.retention_days", 7)
            expires_at = datetime.utcnow() + timedelta(days=retention_days)

            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    job = Job(
                        uuid=uuid,
                        state="queued",
                        task_id=None,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        expires_at=expires_at,
                        ot_traceid=ot_traceid,
                        ot_spanid=ot_spanid
                    )
                    db.add(job)
                    db.commit()
            else:
                job_record = {
                    "uuid": uuid,
                    "state": "queued",
                    "task_id": None,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                    "expires_at": expires_at,
                    "ot_traceid": ot_traceid,
                    "ot_spanid": ot_spanid
                }
                self.jobs[uuid] = job_record

            logger.info(f"Job {uuid} created successfully")
            return True
        except SQLAlchemyError as e:
            logger.error(f"Error creating job {uuid}: {str(e)}")
            return False
    
    def update_job_state(self, uuid: str, step: str, state: str, task_id: Optional[str] = None):
        """
        Update job state.

        Args:
            uuid: Job UUID
            step: Current processing step
            state: New state
            task_id: Celery task ID (optional)
        """
        try:
            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    job = db.query(Job).filter(Job.uuid == uuid).first()
                    if job:
                        job.state = state
                        job.step = step
                        job.updated_at = datetime.utcnow()
                        if task_id:
                            job.task_id = task_id
                        db.commit()
            else:
                if uuid in self.jobs:
                    update_data = {
                        "state": state,
                        "step": step,
                        "updated_at": datetime.utcnow()
                    }
                    if task_id:
                        update_data["task_id"] = task_id
                    self.jobs[uuid].update(update_data)

            upperState = state.upper()
            upperStep = step.upper()
            logger.info(f"Job {uuid} state: {upperStep}/{upperState}")
        except SQLAlchemyError as e:
            logger.error(f"Error updating job {uuid} state: {str(e)}")
    
    def get_job(self, uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get job information.

        Args:
            uuid: Job UUID

        Returns:
            dict: Job information or None if not found
        """
        try:
            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    job = db.query(Job).filter(Job.uuid == uuid).first()
                    if job:
                        return {
                            "uuid": job.uuid,
                            "state": job.state,
                            "step": job.step,
                            "task_id": job.task_id,
                            "created_at": job.created_at,
                            "updated_at": job.updated_at,
                            "expires_at": job.expires_at,
                            "ot_traceid": job.ot_traceid,
                            "ot_spanid": job.ot_spanid,
                            "error_message": job.error_message
                        }
                    return None
            else:
                return self.jobs.get(uuid)
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving job {uuid}: {str(e)}")
            return None
    
    def get_jobs_by_state(self, state: str) -> list:
        """
        Get all jobs with a specific state.

        Args:
            state: Job state to filter by

        Returns:
            list: List of jobs with the specified state
        """
        try:
            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    jobs = db.query(Job).filter(Job.state == state).all()
                    return [
                        {
                            "uuid": job.uuid,
                            "state": job.state,
                            "step": job.step,
                            "task_id": job.task_id,
                            "created_at": job.created_at,
                            "updated_at": job.updated_at,
                            "expires_at": job.expires_at,
                            "ot_traceid": job.ot_traceid,
                            "ot_spanid": job.ot_spanid,
                            "error_message": job.error_message
                        }
                        for job in jobs
                    ]
            else:
                return [job for job in self.jobs.values() if job.get("state") == state]
        except SQLAlchemyError as e:
            logger.error(f"Error retrieving jobs with state {state}: {str(e)}")
            return []
    
    def get_job_count_by_state(self, state: str) -> int:
        """
        Get count of jobs with a specific state.

        Args:
            state: Job state to filter by

        Returns:
            int: Count of jobs with the specified state
        """
        try:
            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    return db.query(Job).filter(Job.state == state).count()
            else:
                return len([job for job in self.jobs.values() if job.get("state") == state])
        except SQLAlchemyError as e:
            logger.error(f"Error counting jobs with state {state}: {str(e)}")
            return 0
    
    def cleanup_expired_jobs(self):
        """Clean up expired jobs."""
        try:
            if self.storage_type == "postgresql":
                with db_manager.get_session() as db:
                    current_time = datetime.utcnow()
                    expired_jobs = db.query(Job).filter(Job.expires_at < current_time).all()
                    count = len(expired_jobs)

                    for job in expired_jobs:
                        db.delete(job)

                    db.commit()
                    logger.info(f"Cleaned up {count} expired jobs from PostgreSQL")
            else:
                # Manual cleanup for in-memory storage
                current_time = datetime.utcnow()
                expired_jobs = [
                    uuid for uuid, job in self.jobs.items()
                    if job.get("expires_at") and job["expires_at"] < current_time
                ]

                for uuid in expired_jobs:
                    del self.jobs[uuid]

                logger.info(f"Cleaned up {len(expired_jobs)} expired jobs from memory")
        except SQLAlchemyError as e:
            logger.error(f"Error cleaning up expired jobs: {str(e)}")

# Create global job tracker instance
job_tracker = JobTracker()