from math import log
from pymongo import MongoClient
from pymongo.errors import PyMongoError
from app.config.settings import config
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

class JobTracker:
    """Job tracking system using MongoDB."""
    
    def __init__(self):
        """Initialize job tracker with MongoDB connection."""
        self.storage_type = config.get("job_tracking.storage_type", "memory")
        
        if self.storage_type == "mongodb":
            self._init_mongodb()
        else:
            # In-memory storage for development
            self.jobs = {}
    
    def _init_mongodb(self):
        """Initialize MongoDB connection."""
        try:
            mongodb_config = config.get("mongodb")
            mongodb_job_config = config.get("job_tracking.mongodb")
            
            # Build connection string
            host = mongodb_job_config.get("host", mongodb_config.get("host", "localhost"))
            port = mongodb_job_config.get("port", mongodb_config.get("port", 27017))
            username = mongodb_job_config.get("username", mongodb_config.get("username", ""))
            password = mongodb_job_config.get("password", mongodb_config.get("password", ""))
            database = mongodb_job_config.get("database", "object_detection")
            collection = mongodb_job_config.get("collection", "jobs")
            
            # Create MongoDB client
            if username == "" or password == "":
                connection_string = f"mongodb://{host}:{port}/"
            else:
                connection_string = f"mongodb://{username}:{password}@{host}:{port}/"

            self.client = MongoClient(connection_string)
            self.db = self.client[database]
            self.collection = self.db[collection]
            
            # Create indexes for better performance
            self.collection.create_index("uuid", unique=True)
            self.collection.create_index("state")
            self.collection.create_index("expires_at", expireAfterSeconds=0)
            
            logger.info("MongoDB job tracker initialized successfully")
        except PyMongoError as e:
            logger.error(f"Failed to initialize MongoDB job tracker: {str(e)}")
            raise
    
    def job_exists(self, uuid: str) -> bool:
        """
        Check if a job with the given UUID already exists.
        
        Args:
            uuid: Job UUID
            
        Returns:
            bool: True if job exists, False otherwise
        """
        try:
            if self.storage_type == "mongodb":
                return self.collection.find_one({"uuid": uuid}) is not None
            else:
                return uuid in self.jobs
        except PyMongoError as e:
            logger.error(f"Error checking job existence for UUID {uuid}: {str(e)}")
            # In case of database error, assume job exists to prevent duplicates
            return True
    
    def create_job(self, uuid: str, ot_traceid: str, ot_spanid: str) -> bool:
        """
        Create a new job record.
        
        Args:
            uuid: Job UUID
            ot_traceid: OpenTelemetry trace ID
            ot_spanid: OpenTelemetry span ID
            
        Returns:
            bool: True if job was created, False if job already exists
        """
        try:
            # Check if job already exists
            if self.job_exists(uuid):
                return False
            
            # Calculate expiration time
            retention_days = config.get("job_tracking.retention_days", 7)
            expires_at = datetime.utcnow() + timedelta(days=retention_days)
            
            job_record = {
                "uuid": uuid,
                "state": "queued",
                "task_id": None,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "expires_at": expires_at,
                "ot_traceid": ot_traceid,
                "ot_spanid": ot_spanid
            }
            
            if self.storage_type == "mongodb":
                self.collection.insert_one(job_record)
            else:
                self.jobs[uuid] = job_record
            
            logger.info(f"Job {uuid} created successfully")
            return True
        except PyMongoError as e:
            logger.error(f"Error creating job {uuid}: {str(e)}")
            return False
    
    def update_job_state(self, uuid: str, step: str, state: str, task_id: Optional[str] = None):
        """
        Update job state.
        
        Args:
            uuid: Job UUID
            state: New state
            task_id: Celery task ID (optional)
        """
        try:
            update_data = {
                "state": state,
                "step": step,
                "updated_at": datetime.utcnow()
            }
            
            if task_id:
                update_data["task_id"] = task_id
            
            if self.storage_type == "mongodb":
                self.collection.update_one(
                    {"uuid": uuid},
                    {"$set": update_data}
                )
            else:
                if uuid in self.jobs:
                    self.jobs[uuid].update(update_data)

            upperState = state.upper()
            upperStep = step.upper()
            logger.info(f"Job {uuid} state: {upperStep}/{upperState}")
        except PyMongoError as e:
            logger.error(f"Error updating job {uuid} state: {str(e)}")
    
    def get_job(self, uuid: str) -> Optional[Dict[str, Any]]:
        """
        Get job information.
        
        Args:
            uuid: Job UUID
            
        Returns:
            dict: Job information or None if not found
        """
        try:
            if self.storage_type == "mongodb":
                return self.collection.find_one({"uuid": uuid})
            else:
                return self.jobs.get(uuid)
        except PyMongoError as e:
            logger.error(f"Error retrieving job {uuid}: {str(e)}")
            return None
    
    def get_jobs_by_state(self, state: str) -> list:
        """
        Get all jobs with a specific state.
        
        Args:
            state: Job state to filter by
            
        Returns:
            list: List of jobs with the specified state
        """
        try:
            if self.storage_type == "mongodb":
                return list(self.collection.find({"state": state}))
            else:
                return [job for job in self.jobs.values() if job.get("state") == state]
        except PyMongoError as e:
            logger.error(f"Error retrieving jobs with state {state}: {str(e)}")
            return []
    
    def get_job_count_by_state(self, state: str) -> int:
        """
        Get count of jobs with a specific state.
        
        Args:
            state: Job state to filter by
            
        Returns:
            int: Count of jobs with the specified state
        """
        try:
            if self.storage_type == "mongodb":
                return self.collection.count_documents({"state": state})
            else:
                return len([job for job in self.jobs.values() if job.get("state") == state])
        except PyMongoError as e:
            logger.error(f"Error counting jobs with state {state}: {str(e)}")
            return 0
    
    def cleanup_expired_jobs(self):
        """Clean up expired jobs (MongoDB handles this automatically with TTL index)."""
        if self.storage_type == "mongodb":
            # MongoDB handles cleanup automatically with TTL index
            logger.info("MongoDB handles expired job cleanup automatically")
        else:
            # Manual cleanup for in-memory storage
            current_time = datetime.utcnow()
            expired_jobs = [
                uuid for uuid, job in self.jobs.items()
                if job.get("expires_at") and job["expires_at"] < current_time
            ]
            
            for uuid in expired_jobs:
                del self.jobs[uuid]
            
            logger.info(f"Cleaned up {len(expired_jobs)} expired jobs from memory")

# Create global job tracker instance
job_tracker = JobTracker()