from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

# OpenTelemetry models
class OpenTelemetryInfo(BaseModel):
    trace_id: str
    span_id: str

# Resource models
class ResourceInfo(BaseModel):
    method: str
    url: str

# File models
class FileInfo(BaseModel):
    uuid: str
    timestamp: int
    size: int
    filename: str
    file_format: str

class FileReference(BaseModel):
    uuid: str
    meta: Dict[str, Any]

class KnownPerson(BaseModel):
    uuid: str
    name: str
    meta: Dict[str, Any]
    files: List[FileReference]

class DatabaseElements(BaseModel):
    known_people: List[KnownPerson]

# Detection configuration models
class PersonDetectionConfig(BaseModel):
    enabled: bool

class PackageDetectionConfig(BaseModel):
    enabled: bool

class AnimalDetectionConfig(BaseModel):
    enabled: bool
    items: List[str]

class FaceRecognitionConfig(BaseModel):
    enabled: bool
    items: List[str]

class BarcodeDetectionConfig(BaseModel):
    enabled: bool

class VehicleDetectionConfig(BaseModel):
    enabled: bool
    items: List[str]

class PlateDetectionConfig(BaseModel):
    enabled: bool

class HotZonePoint(BaseModel):
    x: int
    y: int

class HotZone(BaseModel):
    id: int
    maxwidth: int
    maxheight: int
    points: List[HotZonePoint]

class HotZonesConfig(BaseModel):
    enabled: bool
    zones: List[HotZone]

class DetectionConfiguration(BaseModel):
    standard_skip_frame: str
    package_skip_frame: str
    global_threshold: float
    person_detection: PersonDetectionConfig
    package_detection: PackageDetectionConfig
    animal_detection: AnimalDetectionConfig
    face_recognition: FaceRecognitionConfig
    barcode_detection: BarcodeDetectionConfig
    vehicle_detection: VehicleDetectionConfig
    plate_detection: PlateDetectionConfig
    hotzones: HotZonesConfig

class DatabaseInfo(BaseModel):
    elements: DatabaseElements
    configuration: DetectionConfiguration
    files: Dict[str, Any]

class ResponseInfo(BaseModel):
    callback: str

class DetectionRequest(BaseModel):
    ot: OpenTelemetryInfo
    resources: ResourceInfo
    file: FileInfo
    db: DatabaseInfo
    response: ResponseInfo

class DetectionRequestHeaders(BaseModel):
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None

class DetectionResponse(BaseModel):
    status: str
    task_id: str
    uuid: str

class ErrorResponse(BaseModel):
    status: str
    message: str

class QueueStatsResponse(BaseModel):
    count: int

class JobListResponse(BaseModel):
    jobs: List[dict]  # Simplified for now

class WorkerInfoResponse(BaseModel):
    workers: List[dict]  # Simplified for now

class JobHistoryResponse(BaseModel):
    job: dict  # Simplified for now

class TaskParameters(BaseModel):
    uuid: str
    video_url: str
    callback_url: str
    object_detection_enabled: bool
    object_detection_confidence_threshold: float
    face_recognition_enabled: bool
    face_recognition_confidence_threshold: float
    license_plate_recognition_enabled: bool
    license_plate_recognition_confidence_threshold: float
    barcode_recognition_enabled: bool
    barcode_recognition_confidence_threshold: float
    detected_image_export: bool
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None

class BoundingBox(BaseModel):
    x: float
    y: float
    width: float
    height: float

class BoundingBoxWithFrame(BaseModel):
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float
    confidence: float

class AggregatedDetectedObject(BaseModel):
    class_name: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedFace(BaseModel):
    name: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedLicensePlate(BaseModel):
    text: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedBarcode(BaseModel):
    text: str
    barcode_type: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedDetectionResult(BaseModel):
    uuid: str
    timestamp: str
    objects: Optional[List[AggregatedDetectedObject]]
    faces: Optional[List[AggregatedRecognizedFace]]
    license_plates: Optional[List[AggregatedRecognizedLicensePlate]]
    barcodes: Optional[List[AggregatedRecognizedBarcode]]
    video_url: str
    ot_traceid: Optional[str]
    ot_spanid: Optional[str]

class CallbackPayload(BaseModel):
    uuid: str
    status: str
    results: Optional[AggregatedDetectionResult]
    error: Optional[str]
    ot_traceid: Optional[str]
    ot_spanid: Optional[str]