from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class DetectionRequest(BaseModel):
    uuid: str
    video_url: str
    callback_url: str
    object_detection_enabled: Optional[bool] = True
    object_detection_confidence_threshold: Optional[float] = 0.5
    face_recognition_enabled: Optional[bool] = True
    face_recognition_confidence_threshold: Optional[float] = 0.6
    license_plate_recognition_enabled: Optional[bool] = True
    license_plate_recognition_confidence_threshold: Optional[float] = 0.5
    barcode_recognition_enabled: Optional[bool] = True
    barcode_recognition_confidence_threshold: Optional[float] = 0.8
    detected_image_export: Optional[bool] = False

class DetectionRequestHeaders(BaseModel):
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None

class DetectionResponse(BaseModel):
    status: str
    task_id: str
    uuid: str

class ErrorResponse(BaseModel):
    status: str
    message: str

class QueueStatsResponse(BaseModel):
    count: int

class JobListResponse(BaseModel):
    jobs: List[dict]  # Simplified for now

class WorkerInfoResponse(BaseModel):
    workers: List[dict]  # Simplified for now

class JobHistoryResponse(BaseModel):
    job: dict  # Simplified for now

class TaskParameters(BaseModel):
    uuid: str
    video_url: str
    callback_url: str
    object_detection_enabled: bool
    object_detection_confidence_threshold: float
    face_recognition_enabled: bool
    face_recognition_confidence_threshold: float
    license_plate_recognition_enabled: bool
    license_plate_recognition_confidence_threshold: float
    barcode_recognition_enabled: bool
    barcode_recognition_confidence_threshold: float
    detected_image_export: bool
    ot_traceid: Optional[str] = None
    ot_spanid: Optional[str] = None

class BoundingBox(BaseModel):
    x: float
    y: float
    width: float
    height: float

class BoundingBoxWithFrame(BaseModel):
    bounding_box: BoundingBox
    frame_number: int
    timestamp: float
    confidence: float

class AggregatedDetectedObject(BaseModel):
    class_name: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedFace(BaseModel):
    name: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedLicensePlate(BaseModel):
    text: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedRecognizedBarcode(BaseModel):
    text: str
    barcode_type: str
    max_confidence: float
    bounding_box: BoundingBoxWithFrame
    image_url: Optional[str]

class AggregatedDetectionResult(BaseModel):
    uuid: str
    timestamp: str
    objects: Optional[List[AggregatedDetectedObject]]
    faces: Optional[List[AggregatedRecognizedFace]]
    license_plates: Optional[List[AggregatedRecognizedLicensePlate]]
    barcodes: Optional[List[AggregatedRecognizedBarcode]]
    video_url: str
    ot_traceid: Optional[str]
    ot_spanid: Optional[str]

class CallbackPayload(BaseModel):
    uuid: str
    status: str
    results: Optional[AggregatedDetectionResult]
    error: Optional[str]
    ot_traceid: Optional[str]
    ot_spanid: Optional[str]