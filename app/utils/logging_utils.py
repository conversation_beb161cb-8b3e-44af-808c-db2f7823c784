import functools
import os
import sys
from typing import Any, Callable, Optional, TypeVar

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker

logger = get_logger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


def format_exception_details(exc: Exception, function_name: str = "") -> str:
    exc_type, exc_obj, exc_tb = sys.exc_info()
    if exc_tb:
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        line_no = exc_tb.tb_lineno
    else:
        fname = "unknown"
        line_no = "unknown"

    if function_name:
        return f"{function_name} Exception: {str(exc)} | file: {fname} | line: {line_no}"
    else:
        return f"Exception: {str(exc)} | file: {fname} | line: {line_no}"


def log_and_raise_exception(exc: Exception, function_name: str = "", logger_instance: Optional[Any] = None) -> None:
    if logger_instance is None:
        logger_instance = logger

    error_msg = format_exception_details(exc, function_name)
    logger_instance.error(error_msg)
    raise Exception(error_msg) from exc


def handle_step_execution(job_uuid: str, step_name: str, logger_instance: Optional[Any] = None):

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if logger_instance is None:
                log = logger
            else:
                log = logger_instance

            try:
                log.info(f"Starting step: {step_name}")
                job_tracker.update_job_state(job_uuid, step_name, "started")

                result = func(*args, **kwargs)

                log.info(f"Completed step: {step_name}")
                job_tracker.update_job_state(job_uuid, step_name, "finished")

                return result

            except Exception as e:
                log.error(f"Failed step: {step_name}")
                job_tracker.update_job_state(job_uuid, step_name, "failed")
                log_and_raise_exception(e, func.__name__, log)

        return wrapper

    return decorator


def safe_execute(function_name: str = "", logger_instance: Optional[Any] = None):

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                name = function_name or func.__name__
                log_and_raise_exception(e, name, logger_instance)

        return wrapper

    return decorator


def filter_and_prepare_detection_result(job_uuid: str, item: str, category: str, filter_items: Optional[list] = None, item_uuid: Optional[str] = None) -> None:

    from app.helper import prepare_detection_result

    if filter_items is not None:
        if item.lower() in [filter_item.lower() for filter_item in filter_items]:
            if item_uuid:
                prepare_detection_result(job_uuid, item, category, item_uuid)
            else:
                prepare_detection_result(job_uuid, item, category)
    else:
        if item_uuid:
            prepare_detection_result(job_uuid, item, category, item_uuid)
        else:
            prepare_detection_result(job_uuid, item, category)


def get_standard_model_prediction_args(conf: float, device: str, visualization: bool = False) -> dict:
    return {
        "save": False,
        "imgsz": 640,
        "conf": conf,
        "stream": False,
        "device": device,
        "verbose": False,
        "show": visualization,
        "show_labels": visualization,
        "show_conf": visualization,
        "show_boxes": visualization,
        "stream_buffer": True,
        "visualize": False,
        "augment": False,
        "agnostic_nms": False,
        "retina_masks": False,
        "save_crop": False,
    }


class StepExecutionContext:

    def __init__(self, job_uuid: str, step_name: str, logger_instance: Optional[Any] = None):
        self.job_uuid = job_uuid
        self.step_name = step_name
        self.logger = logger_instance or logger

    def __enter__(self):
        self.logger.info(f"Starting step: {self.step_name}")
        job_tracker.update_job_state(self.job_uuid, self.step_name, "started")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.logger.error(f"Failed step: {self.step_name}")
            job_tracker.update_job_state(self.job_uuid, self.step_name, "failed")
            # Re-raise with formatted message
            if exc_val:
                error_msg = format_exception_details(exc_val, self.step_name)
                self.logger.error(error_msg)
                # Don't suppress the exception, let it propagate
                return False
        else:
            self.logger.info(f"Completed step: {self.step_name}")
            job_tracker.update_job_state(self.job_uuid, self.step_name, "finished")

        return False  # Don't suppress exceptions


def log_function_entry_exit(logger_instance: Optional[Any] = None):

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log = logger_instance or logger
            func_name = func.__name__

            log.debug(f"Entering function: {func_name}")
            try:
                result = func(*args, **kwargs)
                log.debug(f"Exiting function: {func_name}")
                return result
            except Exception:
                log.debug(f"Exception in function: {func_name}")
                raise

        return wrapper

    return decorator


def create_error_response_dict(status: str, status_code: int, task_id: str, uuid: str, message: str) -> dict:
    return {"status": status, "status_code": status_code, "task_id": task_id, "uuid": uuid, "message": message}


class LogMessages:
    # Step execution messages
    STEP_STARTED = "Starting step: {step_name}"
    STEP_COMPLETED = "Completed step: {step_name}"
    STEP_FAILED = "Failed step: {step_name}"

    # File operations
    FILE_DOWNLOAD_START = "Downloading file from {url} to {path}"
    FILE_DOWNLOAD_SUCCESS = "File downloaded successfully to {path} (size: {size} bytes)"
    FILE_DOWNLOAD_FAILED = "Failed to download file from {url}: {error}"
    FILE_NOT_FOUND = "File not found: {path}"
    FILE_EMPTY = "File is empty: {path}"

    # Model operations
    MODEL_PREDICTION_START = "Starting model prediction with confidence: {conf}, device: {device}"
    MODEL_PREDICTION_SUCCESS = "Model prediction completed. Found {count} detections"
    MODEL_PREDICTION_FAILED = "Model prediction failed: {error}"

    # Detection operations
    DETECTION_FOUND = "Detection found: {item} (category: {category}, confidence: {confidence})"
    DETECTION_FILTERED = "Detection filtered out: {item} (not in filter list)"
    DETECTION_PROCESSED = "Processed {count} detections for category: {category}"

    # Job tracking
    JOB_CREATED = "Job created successfully"
    JOB_QUEUED = "Job queued for processing"
    JOB_PROCESSING = "Job processing started"
    JOB_COMPLETED = "Job completed successfully"
    JOB_FAILED = "Job failed: {error}"
    JOB_EXISTS = "Job already exists"

    # Authentication
    AUTH_TOKEN_VALID = "Authentication token validated successfully"
    AUTH_TOKEN_EXPIRED = "Authentication token has expired"
    AUTH_TOKEN_INVALID = "Invalid authentication token"
    AUTH_MISSING = "Authentication header missing"

    # Validation
    VALIDATION_SUCCESS = "Request validation successful"
    VALIDATION_FAILED = "Request validation failed: {error}"
    MISSING_REQUIRED_FIELD = "Missing required field: {field}"
    INVALID_FIELD_VALUE = "Invalid value for field {field}: {value}"

    # System operations
    SYSTEM_STARTUP = "System starting up"
    SYSTEM_SHUTDOWN = "System shutting down"
    MEMORY_CLEANUP = "Memory cleanup completed"
    CACHE_CLEARED = "Cache cleared successfully"

    @staticmethod
    def format_message(template: str, **kwargs) -> str:
        try:
            return template.format(**kwargs)
        except KeyError as e:
            return f"Log message formatting error - missing parameter: {e}"
        except Exception as e:
            return f"Log message formatting error: {e}"


def log_step_start(step_name: str, logger_instance: Optional[Any] = None, **kwargs):
    log = logger_instance or logger
    message = LogMessages.format_message(LogMessages.STEP_STARTED, step_name=step_name, **kwargs)
    log.info(message)


def log_step_complete(step_name: str, logger_instance: Optional[Any] = None, **kwargs):
    log = logger_instance or logger
    message = LogMessages.format_message(LogMessages.STEP_COMPLETED, step_name=step_name, **kwargs)
    log.info(message)


def log_step_failed(step_name: str, error: str, logger_instance: Optional[Any] = None, **kwargs):
    log = logger_instance or logger
    message = LogMessages.format_message(LogMessages.STEP_FAILED, step_name=step_name, **kwargs)
    log.error(f"{message} - {error}")


def log_file_operation(operation: str, path: str, logger_instance: Optional[Any] = None, **kwargs):
    log = logger_instance or logger

    if operation == "download_start":
        message = LogMessages.format_message(LogMessages.FILE_DOWNLOAD_START, path=path, **kwargs)
        log.info(message)
    elif operation == "download_success":
        message = LogMessages.format_message(LogMessages.FILE_DOWNLOAD_SUCCESS, path=path, **kwargs)
        log.info(message)
    elif operation == "download_failed":
        message = LogMessages.format_message(LogMessages.FILE_DOWNLOAD_FAILED, **kwargs)
        log.error(message)
    elif operation == "not_found":
        message = LogMessages.format_message(LogMessages.FILE_NOT_FOUND, path=path)
        log.error(message)
    elif operation == "empty":
        message = LogMessages.format_message(LogMessages.FILE_EMPTY, path=path)
        log.error(message)


def log_detection_result(item: str, category: str, logger_instance: Optional[Any] = None, **kwargs):
    log = logger_instance or logger
    message = LogMessages.format_message(LogMessages.DETECTION_FOUND, item=item, category=category, **kwargs)
    log.info(message)
