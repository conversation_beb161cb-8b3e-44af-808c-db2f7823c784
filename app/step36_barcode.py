from typing import List, Dict, Any
from app.config.settings import config
import logging
import cv2
from pyzbar import pyzbar
import numpy as np

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

class BarcodeRecognizer:
    """Barcode recognizer using pyzbar."""
    
    def __init__(self):
        """Initialize the barcode recognizer."""
        self.confidence_threshold = None
        self.formats = None
        self.preprocess_enabled = None
        self.preprocess_options = None
        self.process_every_n_frames = None
        
    def load_model(self, confidence_threshold: float = None, formats: List[str] = None,
                   preprocess_enabled: bool = None, preprocess_options: Dict[str, bool] = None,
                   process_every_n_frames: int = None):
        """
        Configure the barcode recognizer.
        
        Args:
            confidence_threshold: Minimum confidence threshold for detections
            formats: List of barcode formats to recognize
            preprocess_enabled: Whether to preprocess images
            preprocess_options: Preprocessing options
            process_every_n_frames: Process every Nth frame
        """
        # Use configuration values if not provided
        self.confidence_threshold = confidence_threshold or config.get("barcode_recognition.confidence_threshold", 0.8)
        self.formats = formats or config.get("barcode_recognition.formats", ["QR_CODE", "CODE128", "EAN13", "CODE39"])
        self.preprocess_enabled = preprocess_enabled or config.get("barcode_recognition.preprocess_enabled", True)
        self.preprocess_options = preprocess_options or config.get("barcode_recognition.preprocess_options", {
            "grayscale": True,
            "blur": False,
            "threshold": True
        })
        self.process_every_n_frames = process_every_n_frames or config.get("barcode_recognition.process_every_n_frames", 20)
        
        logger.info("Barcode recognizer configured successfully")
    
    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better barcode recognition.
        
        Args:
            image: Input image
            
        Returns:
            Preprocessed image
        """
        if not self.preprocess_enabled:
            return image
        
        # Convert to grayscale
        if self.preprocess_options.get("grayscale", True):
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply blur
        if self.preprocess_options.get("blur", False):
            image = cv2.GaussianBlur(image, (5, 5), 0)
        
        # Apply threshold
        if self.preprocess_options.get("threshold", True):
            _, image = cv2.threshold(image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return image
    
    def detect_barcodes(self, video_path: str) -> List[Dict[str, Any]]:
        """
        Detect and decode barcodes in a video.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of decoded barcodes with timestamps
        """
        logger.info(f"Starting barcode recognition on video: {video_path}")
        
        # Open video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Failed to open video file: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"Video properties - FPS: {fps}, Total frames: {total_frames}")
        
        decoded_barcodes = []
        frame_count = 0
        
        try:
            while True:
                # Read frame
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process every Nth frame
                if frame_count % self.process_every_n_frames != 0:
                    frame_count += 1
                    continue
                
                # Calculate timestamp
                timestamp = frame_count / fps
                
                # Preprocess frame for barcode detection
                processed_frame = self.preprocess_image(frame)
                
                # Decode barcodes in frame
                barcodes = pyzbar.decode(processed_frame)
                
                # Process results
                for barcode in barcodes:
                    # Get barcode type
                    barcode_type = barcode.type
                    
                    # Check if barcode type is in allowed formats
                    if self.formats and barcode_type not in self.formats:
                        continue
                    
                    # Get barcode data
                    barcode_data = barcode.data.decode("utf-8")
                    
                    # Get bounding box
                    (x, y, w, h) = barcode.rect
                    
                    # Add to decoded barcodes (using 1.0 as confidence since pyzbar doesn't provide it)
                    decoded_barcodes.append({
                        "text": barcode_data,
                        "barcode_type": barcode_type,
                        "confidence": 1.0,  # pyzbar doesn't provide confidence, so we use 1.0
                        "bounding_box": {
                            "x": float(x),
                            "y": float(y),
                            "width": float(w),
                            "height": float(h)
                        },
                        "frame_number": frame_count,
                        "timestamp": timestamp
                    })
                
                frame_count += 1
                
                # Log progress
                if frame_count % 100 == 0:
                    logger.info(f"Processed {frame_count}/{total_frames} frames")
        
        finally:
            # Release video capture
            cap.release()
        
        logger.info(f"Barcode recognition completed. Found {len(decoded_barcodes)} barcodes")
        return decoded_barcodes

# Global barcode recognizer instance
barcode_recognizer = BarcodeRecognizer()

def recognize_barcodes(video_path: str, confidence_threshold: float = None) -> List[Dict[str, Any]]:
    """
    Recognize barcodes in a video.
    
    Args:
        video_path: Path to the video file
        confidence_threshold: Minimum confidence threshold for detections (not used by pyzbar)
        
    Returns:
        List of decoded barcodes with timestamps
    """
    # Configure recognizer if not already configured
    if barcode_recognizer.confidence_threshold is None:
        barcode_recognizer.load_model(confidence_threshold=confidence_threshold)
    
    # Detect barcodes
    return barcode_recognizer.detect_barcodes(video_path)