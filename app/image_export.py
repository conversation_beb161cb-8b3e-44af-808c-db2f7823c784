from typing import List, Dict, Any, Optional
from app.config.settings import config
import logging
import cv2
import boto3
from botocore.exceptions import ClientError
from PIL import Image
import io
import os
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

class ImageExporter:
    """Export detected images to S3-compatible storage."""
    
    def __init__(self):
        """Initialize the image exporter."""
        self.s3_client = None
        self.s3_config = None
        self.image_config = None
        self.url_config = None
        
    def load_config(self, s3_config: Dict[str, Any] = None, image_config: Dict[str, Any] = None,
                   url_config: Dict[str, Any] = None):
        """
        Load S3 and image configuration.
        
        Args:
            s3_config: S3 configuration
            image_config: Image configuration
            url_config: URL configuration
        """
        # Use configuration values if not provided
        self.s3_config = s3_config or config.get("image_export.s3", {})
        self.image_config = image_config or config.get("image_export.image", {})
        self.url_config = url_config or config.get("image_export.url", {})
        
        # Initialize S3 client
        try:
            self.s3_client = boto3.client(
                's3',
                endpoint_url=self.s3_config.get("endpoint_url"),
                aws_access_key_id=self.s3_config.get("access_key_id"),
                aws_secret_access_key=self.s3_config.get("secret_access_key"),
                region_name=self.s3_config.get("region_name")
            )
            logger.info("S3 client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize S3 client: {str(e)}")
            raise
    
    def crop_image(self, frame: Any, bounding_box: Dict[str, float], padding: int = 0) -> Any:
        """
        Crop image based on bounding box with optional padding.
        
        Args:
            frame: Video frame
            bounding_box: Bounding box coordinates
            padding: Padding around bounding box
            
        Returns:
            Cropped image
        """
        x = int(bounding_box["x"])
        y = int(bounding_box["y"])
        width = int(bounding_box["width"])
        height = int(bounding_box["height"])
        
        # Apply padding
        x = max(0, x - padding)
        y = max(0, y - padding)
        width = min(frame.shape[1] - x, width + 2 * padding)
        height = min(frame.shape[0] - y, height + 2 * padding)
        
        # Crop image
        cropped = frame[y:y+height, x:x+width]
        return cropped
    
    def resize_image(self, image: Any, max_width: int, max_height: int) -> Any:
        """
        Resize image while maintaining aspect ratio.
        
        Args:
            image: Input image
            max_width: Maximum width
            max_height: Maximum height
            
        Returns:
            Resized image
        """
        height, width = image.shape[:2]
        
        # Calculate new dimensions
        if width > max_width or height > max_height:
            ratio = min(max_width / width, max_height / height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        return image
    
    def process_image(self, image: Any) -> bytes:
        """
        Process image for export (convert to specified format, apply quality settings).
        
        Args:
            image: Input image
            
        Returns:
            Processed image as bytes
        """
        format = self.image_config.get("format", "JPEG")
        quality = self.image_config.get("quality", 85)
        
        # Convert to PIL Image
        if len(image.shape) == 3:
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(image)
        
        # Save to bytes
        img_buffer = io.BytesIO()
        if format.upper() == "JPEG":
            pil_image.save(img_buffer, format="JPEG", quality=quality, optimize=True)
        else:
            pil_image.save(img_buffer, format=format)
        
        return img_buffer.getvalue()
    
    def upload_image(self, image_bytes: bytes, bucket_name: str, object_key: str) -> str:
        """
        Upload image to S3 and return URL.
        
        Args:
            image_bytes: Image data as bytes
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            Image URL
        """
        try:
            # Upload to S3
            self.s3_client.put_object(
                Bucket=bucket_name,
                Key=object_key,
                Body=image_bytes,
                ContentType=f"image/{self.image_config.get('format', 'jpeg').lower()}"
            )
            
            # Generate URL
            public_access = self.url_config.get("public_access", True)
            if public_access:
                # For public access, generate direct URL
                endpoint_url = self.s3_config.get("endpoint_url", "").rstrip('/')
                bucket_name = self.s3_config.get("bucket_name", "")
                url = f"{endpoint_url}/{bucket_name}/{object_key}"
            else:
                # For private access, generate presigned URL
                expiration_hours = self.url_config.get("expiration_hours", 24)
                expiration = timedelta(hours=expiration_hours)
                url = self.s3_client.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': bucket_name, 'Key': object_key},
                    ExpiresIn=int(expiration.total_seconds())
                )
            
            return url
            
        except ClientError as e:
            logger.error(f"Failed to upload image to S3: {str(e)}")
            raise
    
    def export_detected_images(self, video_path: str, detection_result: Dict[str, Any], 
                              uuid: str) -> Dict[str, str]:
        """
        Export detected images to S3.
        
        Args:
            video_path: Path to the video file
            detection_result: Detection results
            uuid: Job UUID
            
        Returns:
            Dictionary mapping detection IDs to S3 URLs
        """
        if self.s3_client is None:
            raise ValueError("S3 client not initialized. Call load_config() first.")
        
        logger.info(f"Starting image export for UUID: {uuid}")
        
        # Open video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Failed to open video file: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        image_urls = {}
        exported_count = 0
        
        try:
            # Export object images
            if "objects" in detection_result and detection_result["objects"]:
                for i, obj in enumerate(detection_result["objects"]):
                    frame_number = obj["frame_number"]
                    timestamp = obj["timestamp"]
                    class_name = obj["class_name"]
                    confidence = obj["confidence"]
                    
                    # Extract frame
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                    ret, frame = cap.read()
                    if not ret:
                        continue
                    
                    # Crop image
                    padding = self.image_config.get("padding", 20)
                    cropped = self.crop_image(frame, obj["bounding_box"], padding)
                    
                    # Resize image
                    max_width = self.image_config.get("max_width", 800)
                    max_height = self.image_config.get("max_height", 600)
                    resized = self.resize_image(cropped, max_width, max_height)
                    
                    # Process image
                    image_bytes = self.process_image(resized)
                    
                    # Upload to S3
                    bucket_name = self.s3_config.get("bucket_name", "")
                    path_prefix = self.s3_config.get("path_prefix", "detections")
                    format = self.image_config.get("format", "jpeg").lower()
                    object_key = f"{path_prefix}/{uuid}/objects/{timestamp}_{confidence:.2f}.{format}"
                    
                    url = self.upload_image(image_bytes, bucket_name, object_key)
                    image_urls[f"object_{i}"] = url
                    exported_count += 1
            
            # Export face images
            if "faces" in detection_result and detection_result["faces"]:
                for i, face in enumerate(detection_result["faces"]):
                    frame_number = face["frame_number"]
                    timestamp = face["timestamp"]
                    name = face["name"]
                    confidence = face["confidence"]
                    
                    # Extract frame
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                    ret, frame = cap.read()
                    if not ret:
                        continue
                    
                    # Crop image
                    padding = self.image_config.get("padding", 20)
                    cropped = self.crop_image(frame, face["bounding_box"], padding)
                    
                    # Resize image
                    max_width = self.image_config.get("max_width", 800)
                    max_height = self.image_config.get("max_height", 600)
                    resized = self.resize_image(cropped, max_width, max_height)
                    
                    # Process image
                    image_bytes = self.process_image(resized)
                    
                    # Upload to S3
                    bucket_name = self.s3_config.get("bucket_name", "")
                    path_prefix = self.s3_config.get("path_prefix", "detections")
                    format = self.image_config.get("format", "jpeg").lower()
                    object_key = f"{path_prefix}/{uuid}/faces/{timestamp}_{confidence:.2f}.{format}"
                    
                    url = self.upload_image(image_bytes, bucket_name, object_key)
                    image_urls[f"face_{i}"] = url
                    exported_count += 1
            
            # Export license plate images
            if "license_plates" in detection_result and detection_result["license_plates"]:
                for i, plate in enumerate(detection_result["license_plates"]):
                    frame_number = plate["frame_number"]
                    timestamp = plate["timestamp"]
                    text = plate["text"]
                    confidence = plate["confidence"]
                    
                    # Extract frame
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                    ret, frame = cap.read()
                    if not ret:
                        continue
                    
                    # Crop image
                    padding = self.image_config.get("padding", 20)
                    cropped = self.crop_image(frame, plate["bounding_box"], padding)
                    
                    # Resize image
                    max_width = self.image_config.get("max_width", 800)
                    max_height = self.image_config.get("max_height", 600)
                    resized = self.resize_image(cropped, max_width, max_height)
                    
                    # Process image
                    image_bytes = self.process_image(resized)
                    
                    # Upload to S3
                    bucket_name = self.s3_config.get("bucket_name", "")
                    path_prefix = self.s3_config.get("path_prefix", "detections")
                    format = self.image_config.get("format", "jpeg").lower()
                    object_key = f"{path_prefix}/{uuid}/license_plates/{timestamp}_{confidence:.2f}.{format}"
                    
                    url = self.upload_image(image_bytes, bucket_name, object_key)
                    image_urls[f"license_plate_{i}"] = url
                    exported_count += 1
            
            # Export barcode images
            if "barcodes" in detection_result and detection_result["barcodes"]:
                for i, barcode in enumerate(detection_result["barcodes"]):
                    frame_number = barcode["frame_number"]
                    timestamp = barcode["timestamp"]
                    text = barcode["text"]
                    confidence = barcode["confidence"]
                    
                    # Extract frame
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                    ret, frame = cap.read()
                    if not ret:
                        continue
                    
                    # Crop image
                    padding = self.image_config.get("padding", 20)
                    cropped = self.crop_image(frame, barcode["bounding_box"], padding)
                    
                    # Resize image
                    max_width = self.image_config.get("max_width", 800)
                    max_height = self.image_config.get("max_height", 600)
                    resized = self.resize_image(cropped, max_width, max_height)
                    
                    # Process image
                    image_bytes = self.process_image(resized)
                    
                    # Upload to S3
                    bucket_name = self.s3_config.get("bucket_name", "")
                    path_prefix = self.s3_config.get("path_prefix", "detections")
                    format = self.image_config.get("format", "jpeg").lower()
                    object_key = f"{path_prefix}/{uuid}/barcodes/{timestamp}_{confidence:.2f}.{format}"
                    
                    url = self.upload_image(image_bytes, bucket_name, object_key)
                    image_urls[f"barcode_{i}"] = url
                    exported_count += 1
        
        finally:
            # Release video capture
            cap.release()
        
        logger.info(f"Image export completed. Exported {exported_count} images for UUID: {uuid}")
        return image_urls

# Global image exporter instance
image_exporter = ImageExporter()

def export_detected_images(video_path: str, detection_result: Dict[str, Any], uuid: str) -> Dict[str, str]:
    """
    Export detected images to S3.
    
    Args:
        video_path: Path to the video file
        detection_result: Detection results
        uuid: Job UUID
        
    Returns:
        Dictionary mapping detection IDs to S3 URLs
    """
    # Load config if not already loaded
    if image_exporter.s3_client is None:
        image_exporter.load_config()
    
    # Export images
    return image_exporter.export_detected_images(video_path, detection_result, uuid)