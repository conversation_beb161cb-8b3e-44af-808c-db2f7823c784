from typing import List, Dict, Any
from app.config.settings import config
import logging
import cv2
import face_recognition
import os
import numpy as np

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

class FaceRecognizer:
    """Face recognition using the face_recognition library."""
    
    def __init__(self):
        """Initialize the face recognizer."""
        self.known_face_encodings = []
        self.known_face_names = []
        self.tolerance = None
        self.model = None
        self.upsample_times = None
        self.num_jitters = None
        self.process_every_n_frames = None
        
    def load_known_faces(self, known_faces_directory: str = None, encodings_file: str = None):
        """
        Load known face encodings from directory or file.
        
        Args:
            known_faces_directory: Directory containing known face images
            encodings_file: JSON file containing pre-computed face encodings
        """
        # Use configuration values if not provided
        known_faces_directory = known_faces_directory or config.get("face_recognition.known_faces_directory", "known_faces")
        encodings_file = encodings_file or config.get("face_recognition.encodings_file", "known_faces/encodings.json")
        
        # Try to load from encodings file first
        if os.path.exists(encodings_file):
            try:
                with open(encodings_file, 'r') as f:
                    encodings_data = json.load(f)
                    self.known_face_encodings = [np.array(encoding) for encoding in encodings_data["encodings"]]
                    self.known_face_names = encodings_data["names"]
                logger.info(f"Loaded {len(self.known_face_names)} known faces from {encodings_file}")
                return
            except Exception as e:
                logger.warning(f"Failed to load encodings from {encodings_file}: {str(e)}")
        
        # If encodings file doesn't exist or failed to load, generate from directory
        if os.path.exists(known_faces_directory):
            self.known_face_encodings = []
            self.known_face_names = []
            
            for person_name in os.listdir(known_faces_directory):
                person_dir = os.path.join(known_faces_directory, person_name)
                if not os.path.isdir(person_dir):
                    continue
                
                logger.info(f"Loading faces for {person_name}")
                
                for image_file in os.listdir(person_dir):
                    if not image_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        continue
                    
                    image_path = os.path.join(person_dir, image_file)
                    try:
                        image = face_recognition.load_image_file(image_path)
                        encodings = face_recognition.face_encodings(image)
                        
                        if len(encodings) > 0:
                            self.known_face_encodings.append(encodings[0])
                            self.known_face_names.append(person_name)
                    except Exception as e:
                        logger.warning(f"Failed to process {image_path}: {str(e)}")
            
            logger.info(f"Loaded {len(self.known_face_names)} known faces from {known_faces_directory}")
            
            # Save encodings to file for future use
            try:
                encodings_data = {
                    "encodings": [encoding.tolist() for encoding in self.known_face_encodings],
                    "names": self.known_face_names
                }
                with open(encodings_file, 'w') as f:
                    json.dump(encodings_data, f)
                logger.info(f"Saved encodings to {encodings_file}")
            except Exception as e:
                logger.warning(f"Failed to save encodings to {encodings_file}: {str(e)}")
        else:
            logger.warning(f"Known faces directory {known_faces_directory} does not exist")
    
    def recognize_faces(self, video_path: str, confidence_threshold: float = None) -> List[Dict[str, Any]]:
        """
        Recognize faces in a video.
        
        Args:
            video_path: Path to the video file
            confidence_threshold: Minimum confidence threshold for face recognition
            
        Returns:
            List of recognized faces with timestamps
        """
        # Use configuration values if not provided
        confidence_threshold = confidence_threshold or config.get("face_recognition.tolerance", 0.6)
        self.tolerance = confidence_threshold
        self.model = config.get("face_recognition.model", "hog")
        self.upsample_times = config.get("face_recognition.upsample_times", 1)
        self.num_jitters = config.get("face_recognition.num_jitters", 1)
        self.process_every_n_frames = config.get("face_recognition.process_every_n_frames", 10)
        
        logger.info(f"Starting face recognition on video: {video_path}")
        
        # Open video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Failed to open video file: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"Video properties - FPS: {fps}, Total frames: {total_frames}")
        
        recognized_faces = []
        frame_count = 0
        
        try:
            while True:
                # Read frame
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process every Nth frame
                if frame_count % self.process_every_n_frames != 0:
                    frame_count += 1
                    continue
                
                # Convert BGR to RGB
                rgb_frame = frame[:, :, ::-1]
                
                # Calculate timestamp
                timestamp = frame_count / fps
                
                # Find face locations and encodings
                face_locations = face_recognition.face_locations(rgb_frame, 
                                                               model=self.model, 
                                                               number_of_times_to_upsample=self.upsample_times)
                face_encodings = face_recognition.face_encodings(rgb_frame, face_locations, 
                                                               num_jitters=self.num_jitters)
                
                # Recognize faces
                for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
                    # Calculate distance to known faces
                    if len(self.known_face_encodings) > 0:
                        distances = face_recognition.face_distance(self.known_face_encodings, face_encoding)
                        best_match_index = np.argmin(distances)
                        confidence = 1 - distances[best_match_index]
                        
                        if confidence >= (1 - self.tolerance):
                            name = self.known_face_names[best_match_index]
                        else:
                            name = "unknown"
                    else:
                        name = "unknown"
                        confidence = 0.0
                    
                    # Add to recognized faces
                    recognized_faces.append({
                        "name": name,
                        "confidence": confidence,
                        "bounding_box": {
                            "x": float(left),
                            "y": float(top),
                            "width": float(right - left),
                            "height": float(bottom - top)
                        },
                        "frame_number": frame_count,
                        "timestamp": timestamp
                    })
                
                frame_count += 1
                
                # Log progress
                if frame_count % 100 == 0:
                    logger.info(f"Processed {frame_count}/{total_frames} frames")
        
        finally:
            # Release video capture
            cap.release()
        
        logger.info(f"Face recognition completed. Found {len(recognized_faces)} faces")
        return recognized_faces

# Global face recognizer instance
face_recognizer = FaceRecognizer()

def recognize_faces(video_path: str, confidence_threshold: float = None) -> List[Dict[str, Any]]:
    """
    Recognize faces in a video.
    
    Args:
        video_path: Path to the video file
        confidence_threshold: Minimum confidence threshold for face recognition
        
    Returns:
        List of recognized faces with timestamps
    """
    # Load known faces if not already loaded
    if len(face_recognizer.known_face_encodings) == 0:
        face_recognizer.load_known_faces()
    
    # Recognize faces
    return face_recognizer.recognize_faces(video_path, confidence_threshold)