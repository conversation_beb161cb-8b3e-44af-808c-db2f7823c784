from celery import current_task
from app.celery_app import celery_app, validate_video_file, process_video_content, send_results_to_callback
from app.models import TaskParameters
from app.config.settings import config
from app.job_tracking import job_tracker
import logging
import traceback
import os
import app.step10_downloadfile as step10

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

def process_video(task_params_dict):
    try:
        task_params = TaskParameters(**task_params_dict)
    except Exception as e:
        error_msg = f"Failed to prepare task parameters: {str(e)}"
        logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
        
        # Update job state to failed
        job_tracker.update_job_state(task_params.uuid, "failed")
        
        # Send error to callback URL
        send_results_to_callback(task_params, None, "failed", error_msg)

    
    # Update job state to processing
    job_tracker.update_job_state(task_params.uuid, "tasks", "processing", self.request.id)
    
    # Get temp directory from configuration or use default
    temp_base_dir = config.get("file_processing.temp_directory", "/tmp/object-detection")
    # Create task-specific temporary directory
    temp_dir = os.path.join(temp_base_dir, f"object_detection_{task_params.uuid}")
    video_path = os.path.join(temp_base_dir, f"{task_params.uuid}.video.mp4")
    
    try:
        # Download video with error handling
        try:
            step10.downloadfile_run(task_params.uuid, task_params.step10_downloadFile.resource_url, video_path)
        except Exception as e:
            error_msg = f"Failed to download video from {task_params.step10_downloadFile.resource_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "downloafile", "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise

        ############################################################
        """"
        # Validate video file
        try:
            validate_video_file(video_path)
        except Exception as e:
            error_msg = f"Video validation failed for {task_params.step10_downloadFile.resource_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        
        # Process video
        try:
            detection_result = process_video_content(
                video_path, task_params
            )
        except Exception as e:
            error_msg = f"Video processing failed for {task_params.video_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
               
        # Aggregate results
        try:
            aggregated_result = aggregate_results(
                detection_result, 
                image_urls, 
                task_params.ot_traceid, 
                task_params.ot_spanid
            )
        except Exception as e:
            error_msg = f"Failed to aggregate results for UUID {task_params.uuid}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        
        # Send results to callback URL
        try:
            send_results_to_callback(task_params, aggregated_result, "success", None)
        except Exception as e:
            error_msg = f"Failed to send results to callback URL for UUID {task_params.uuid}: {str(e)}"
            logger.error(f"Error processing video for UUID {task_params.uuid}: {error_msg}")
            
            # Update job state to failed
            job_tracker.update_job_state(task_params.uuid, "failed")
            
            # Send error to callback URL
            send_results_to_callback(task_params, None, "failed", error_msg)
            
            # Re-raise the exception so Celery can handle retries
            raise
        """

        # Update job state to completed
        job_tracker.update_job_state(task_params.uuid, "tasks","completed")

        return {"status": "success", "uuid": task_params.uuid}
        
    except Exception as e:
        logger.error(f"Error processing video for UUID {task_params.uuid}: {str(e)}")
        logger.error(traceback.format_exc())
        
        # Update job state to failed (if not already updated)
        job_tracker.update_job_state(task_params.uuid, "failed")
        
        # Note: Not sending error to callback here since it should have been sent in the specific error handlers above
        
        # Re-raise the exception so Celery can handle retries
        raise
        
    finally:
        # Clean up temporary files
        try:
            if os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up temporary directory for UUID: {task_params.uuid}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory for UUID {task_params.uuid}: {str(e)}")