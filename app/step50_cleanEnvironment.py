from app.models import TaskParameters
from app.job_tracking import job_tracker
import logging, os

from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

def step50_cleanEnvironment_run(task_params: TaskParameters, video_path):

    job_tracker.update_job_state(task_params.uuid, "cleanenv", "started")
    try:
        if os.path.exists(video_path):
            os.remove(video_path)

        job_tracker.update_job_state(task_params.uuid, "cleanenv", "finished")
    except Exception as e:
        raise Exception(f"Unexpected error while clean environment {str(e)}")