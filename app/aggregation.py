from typing import List, Dict, Any, Optional
from app.models import AggregatedDetectionResult, AggregatedDetectedObject, AggregatedRecognizedFace, \
                      AggregatedRecognizedLicensePlate, AggregatedRecognizedBarcode, BoundingBoxWithFrame
from app.config.settings import config
import logging

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

def aggregate_results(detection_result: Dict[str, Any], ot_traceid: str = None, ot_spanid: str = None) -> AggregatedDetectionResult:
    """
    Aggregate detection results by taking the highest confidence detection for each object type.
    
    Args:
        detection_result: Raw detection results
        ot_traceid: OpenTelemetry trace ID
        ot_spanid: OpenTelemetry span ID
        
    Returns:
        Aggregated detection result
    """
    logger.info("Starting result aggregation")
    
    # Initialize lists for aggregated results
    aggregated_objects = []
    aggregated_faces = []
    aggregated_license_plates = []
    aggregated_barcodes = []
    
    # Aggregate objects
    if "objects" in detection_result and detection_result["objects"]:
        # Group objects by class name
        object_groups = {}
        for i, obj in enumerate(detection_result["objects"]):
            class_name = obj["class_name"]
            if class_name not in object_groups:
                object_groups[class_name] = []
            object_groups[class_name].append((i, obj))
        
        # For each group, find the detection with highest confidence
        for class_name, objects in object_groups.items():
            # Sort by confidence (descending)
            objects.sort(key=lambda x: x[1]["confidence"], reverse=True)
            
            # Take the first one (highest confidence)
            best_index, best_object = objects[0]
            
            # Create bounding box with frame
            bounding_box_with_frame = BoundingBoxWithFrame(
                bounding_box=best_object["bounding_box"],
                frame_number=best_object["frame_number"],
                timestamp=best_object["timestamp"],
                confidence=best_object["confidence"]
            )
            
            # Create aggregated object
            aggregated_object = AggregatedDetectedObject(
                class_name=class_name,
                max_confidence=best_object["confidence"],
                bounding_box=bounding_box_with_frame
            )
            
            aggregated_objects.append(aggregated_object)
    
    # Aggregate faces
    if "faces" in detection_result and detection_result["faces"]:
        # Group faces by name
        face_groups = {}
        for i, face in enumerate(detection_result["faces"]):
            name = face["name"]
            if name not in face_groups:
                face_groups[name] = []
            face_groups[name].append((i, face))
        
        # For each group, find the detection with highest confidence
        for name, faces in face_groups.items():
            # Sort by confidence (descending)
            faces.sort(key=lambda x: x[1]["confidence"], reverse=True)
            
            # Take the first one (highest confidence)
            best_index, best_face = faces[0]
            
            # Create bounding box with frame
            bounding_box_with_frame = BoundingBoxWithFrame(
                bounding_box=best_face["bounding_box"],
                frame_number=best_face["frame_number"],
                timestamp=best_face["timestamp"],
                confidence=best_face["confidence"]
            )

            # Create aggregated face
            aggregated_face = AggregatedRecognizedFace(
                name=name,
                max_confidence=best_face["confidence"],
                bounding_box=bounding_box_with_frame
            )
            
            aggregated_faces.append(aggregated_face)
    
    # Aggregate license plates
    if "license_plates" in detection_result and detection_result["license_plates"]:
        # Group license plates by text
        plate_groups = {}
        for i, plate in enumerate(detection_result["license_plates"]):
            text = plate["text"]
            if text not in plate_groups:
                plate_groups[text] = []
            plate_groups[text].append((i, plate))
        
        # For each group, find the detection with highest confidence
        for text, plates in plate_groups.items():
            # Sort by confidence (descending)
            plates.sort(key=lambda x: x[1]["confidence"], reverse=True)
            
            # Take the first one (highest confidence)
            best_index, best_plate = plates[0]
            
            # Create bounding box with frame
            bounding_box_with_frame = BoundingBoxWithFrame(
                bounding_box=best_plate["bounding_box"],
                frame_number=best_plate["frame_number"],
                timestamp=best_plate["timestamp"],
                confidence=best_plate["confidence"]
            )
            
            # Create aggregated license plate
            aggregated_plate = AggregatedRecognizedLicensePlate(
                text=text,
                max_confidence=best_plate["confidence"],
                bounding_box=bounding_box_with_frame
            )
            
            aggregated_license_plates.append(aggregated_plate)
    
    # Aggregate barcodes
    if "barcodes" in detection_result and detection_result["barcodes"]:
        # Group barcodes by text
        barcode_groups = {}
        for i, barcode in enumerate(detection_result["barcodes"]):
            text = barcode["text"]
            if text not in barcode_groups:
                barcode_groups[text] = []
            barcode_groups[text].append((i, barcode))
        
        # For each group, find the detection with highest confidence
        for text, barcodes in barcode_groups.items():
            # Sort by confidence (descending)
            barcodes.sort(key=lambda x: x[1]["confidence"], reverse=True)
            
            # Take the first one (highest confidence)
            best_index, best_barcode = barcodes[0]
            
            # Create bounding box with frame
            bounding_box_with_frame = BoundingBoxWithFrame(
                bounding_box=best_barcode["bounding_box"],
                frame_number=best_barcode["frame_number"],
                timestamp=best_barcode["timestamp"],
                confidence=best_barcode["confidence"]
            )

            # Create aggregated barcode
            aggregated_barcode = AggregatedRecognizedBarcode(
                text=text,
                barcode_type=best_barcode["barcode_type"],
                max_confidence=best_barcode["confidence"],
                bounding_box=bounding_box_with_frame
            )
            
            aggregated_barcodes.append(aggregated_barcode)
    
    # Create aggregated detection result
    aggregated_result = AggregatedDetectionResult(
        uuid=detection_result.get("uuid", ""),
        timestamp=detection_result.get("timestamp", ""),
        video_url=detection_result.get("video_url", ""),
        ot_traceid=ot_traceid,
        ot_spanid=ot_spanid,
        objects=aggregated_objects if aggregated_objects else None,
        faces=aggregated_faces if aggregated_faces else None,
        license_plates=aggregated_license_plates if aggregated_license_plates else None,
        barcodes=aggregated_barcodes if aggregated_barcodes else None
    )
    
    logger.info(f"Result aggregation completed. Aggregated {len(aggregated_objects)} objects, "
                f"{len(aggregated_faces)} faces, {len(aggregated_license_plates)} license plates, "
                f"{len(aggregated_barcodes)} barcodes")
    
    return aggregated_result