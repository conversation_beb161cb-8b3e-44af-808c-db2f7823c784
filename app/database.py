from sqlalchemy import create_engine, Column, String, DateTime, Integer, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql import UUID
from app.config.settings import config
import logging
from datetime import datetime, timedelta
from typing import Optional
import uuid

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

Base = declarative_base()

class Job(Base):
    """Job tracking model for PostgreSQL."""
    __tablename__ = "jobs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    uuid = Column(String(36), unique=True, nullable=False, index=True)
    state = Column(String(50), nullable=False, index=True)
    step = Column(String(50), nullable=True)
    task_id = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False, index=True)
    ot_traceid = Column(String(255), nullable=True)
    ot_spanid = Column(String(255), nullable=True)
    error_message = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<Job(uuid='{self.uuid}', state='{self.state}', step='{self.step}')>"

class DatabaseManager:
    """Database manager for PostgreSQL operations."""
    
    def __init__(self):
        """Initialize database connection."""
        self.engine = None
        self.SessionLocal = None
        self._init_database()
    
    def _init_database(self):
        """Initialize PostgreSQL connection."""
        try:
            postgresql_config = config.get("postgresql", {})
            
            # Build connection string
            host = postgresql_config.get("host", "postgresql")
            port = postgresql_config.get("port", 5432)
            username = postgresql_config.get("username", "postgres")
            password = postgresql_config.get("password", "password")
            database = postgresql_config.get("database", "object_detection")
            
            # Create PostgreSQL connection string
            connection_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"
            
            # Create engine
            self.engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=300,
                echo=config.get("postgresql.echo", False)
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            # Create tables
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("PostgreSQL database manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL database manager: {str(e)}")
            raise
    
    def get_session(self) -> Session:
        """Get database session."""
        return self.SessionLocal()
    
    def close(self):
        """Close database connection."""
        if self.engine:
            self.engine.dispose()

# Create global database manager instance
db_manager = DatabaseManager()

def get_db() -> Session:
    """Dependency to get database session."""
    db = db_manager.get_session()
    try:
        yield db
    finally:
        db.close()
