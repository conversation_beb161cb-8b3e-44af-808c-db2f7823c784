
def step10_downloadfile(uuid:str, resource_url: str, video_path: str):
    """
    Download video from URL to local file.
    
    Args:
        video_url: URL of the video to download
        video_path: Local path to save the video
    """
    logger.info(f"Downloading video from {resource_url} to {video_path}")
    
    # Create directory if it doesn't exist
    # os.makedirs(os.path.dirname(video_path), exist_ok=True)
    
    # Download with timeout
    # timeout = config.get("file_validation.timeout_seconds", 30)
    try:
        with requests.get(resource_url, allow_redirects=True, stream=True) as responseGet:
            if responseGet.ok:
                responseGet.raise_for_status()
                try:
                    with open(video_path, 'wb') as out_file:
                        for chunk in responseGet.iter_content(chunk_size=1024 * 1024 * 2):
                            out_file.write(chunk)
                except Exception as err1:
                    exc_type, exc_obj, exc_tb = sys.exc_info()
                    fname1 = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
                    logger.error (f"step00_downloadFile Exception with error: {err1} uuid:{uuid} file:{fname1} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}")
            else:
                helper.remove_job_queue(uuid)
                logger.error (f"step00_downloadFile Request status_code:{responseGet.status_code} reason:{responseGet.reason} url:{resource_url} uuid:{uuid}")

        return (True, 0, f"step00_downloadFile url:{resource_url} uuid:{uuid} file downloaded")
    except urllib.error.URLError as e:
        raise Exception(f"Failed to download video: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error during video download: {str(e)}")
    
    # Check if file was actually downloaded
    if not os.path.exists(video_path):
        raise Exception("Video file was not downloaded successfully")
    
    # Check file size
    file_size = os.path.getsize(video_path)
    if file_size == 0:
        raise Exception("Downloaded video file is empty")
    
    logger.info(f"Video downloaded successfully to {video_path} (size: {file_size} bytes)")