import logging
import requests
from app.config.settings import config
import os, sys, urllib
from app.job_tracking import job_tracker

logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def downloadfile_run(uuid:str, resource_url: str, video_path: str):
    """
    Download video from URL to local file.
    
    Args:
        video_url: URL of the video to download
        video_path: Local path to save the video
    """
    logger.info(f"Downloading video from {resource_url} to {video_path} {uuid}")
    job_tracker.update_job_state(uuid, "downloadfile", "started")
    try:
        with requests.get(resource_url, allow_redirects=True, stream=True) as responseGet:
            if responseGet.ok:
                responseGet.raise_for_status()
                try:
                    with open(video_path, 'wb') as out_file:
                        for chunk in responseGet.iter_content(chunk_size=1024 * 1024 * 2):
                            out_file.write(chunk)
                except Exception as e:
                    raise Exception(
                        f"downloadFile Request status_code:{responseGet.status_code} reason:{responseGet.reason} url:{resource_url} uuid:{uuid} {str(e)}")
            else:
                raise Exception(f"downloadFile Request status_code:{responseGet.status_code} reason:{responseGet.reason} url:{resource_url} uuid:{uuid}")

    except urllib.error.URLError as e:
        raise Exception(f"Failed to download video: {str(e)}")
    except Exception as e:
        raise Exception(f"downloadFile Unexpected error during video download: {str(e)}")
    
    # Check if file was actually downloaded
    if not os.path.exists(video_path):
        raise Exception("Video file was not downloaded successfully")
    
    # Check file size
    file_size = os.path.getsize(video_path)
    if file_size == 0:
        raise Exception("Downloaded video file is empty")

    job_tracker.update_job_state(uuid, "downloadfile", "finished")
    logger.info(f"Video downloaded successfully to {video_path} (size: {file_size} bytes) {uuid}")