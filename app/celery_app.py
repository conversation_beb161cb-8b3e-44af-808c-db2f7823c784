from celery import Celery
from app.config.settings import config
import logging

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def make_celery():
    """Create and configure Celery application."""
    # Create Celery instance
    celery_app = Celery("object_detection_worker")
    
    # Configure Celery with RabbitMQ as broker
    rabbitmq_config = config.get("rabbitmq")
    broker_url = f"amqp://{rabbitmq_config['username']}:{rabbitmq_config['password']}@{rabbitmq_config['host']}:{rabbitmq_config['port']}{rabbitmq_config['vhost']}"

    mongodb_config = config.get("mongodb")
    if mongodb_config:
        mongodb_username = mongodb_config.get("username", "")
        mongodb_password = mongodb_config.get("password", "")
        mongodb_host = mongodb_config.get("host", "localhost")
        mongodb_port = mongodb_config.get("port", 27017)        
        if mongodb_username == "" or mongodb_password == "":
            result_broker_url = f"mongodb://{mongodb_host}:{mongodb_port}/"
        else:
            result_broker_url = f"mongodb://{mongodb_username}:{mongodb_password}@{mongodb_host}:{mongodb_port}/"
    
    celery_app.conf.update(
        broker_url=broker_url,
        result_backend=result_broker_url,
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        task_routes={
            "app.tasks.process_video": {"queue": "video_processing"}
        },
        worker_prefetch_multiplier=config.get("worker.prefetch_multiplier", 1),
        worker_concurrency=config.get("worker.concurrency", 4),
        worker_max_tasks_per_child=config.get("worker.max_tasks_per_child", 1000)
    )
    
    return celery_app

# Create Celery app instance
celery_app = make_celery()

# Import tasks to register them with Celery
from app import tasks

if __name__ == "__main__":
    celery_app.start()