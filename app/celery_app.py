from celery import Celery, current_task
from app.config.settings import config
import logging
from app.models import TaskParameters, AggregatedDetectionResult
from app.detection import detect_objects
from app.step34_face_recognition import recognize_faces
from app.step35_licence_plate import recognize_license_plates
from app.step36_barcode import recognize_barcodes
from app.aggregation import aggregate_results
from app.step40_sendresult import step40_sendResult_run
from app.step50_cleanEnvironment import step50_cleanEnvironment_run
from app.job_tracking import job_tracker
import uuid
import traceback
from datetime import datetime
import os
import magic
import app.step10_downloadfile as step10
from celery import Task

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

def make_celery():
    celery_app = Celery("object_detection_worker")

    broker_url = ""
    result_broker_url = ""

    rabbitmq_config = config.get("rabbitmq")
    if rabbitmq_config:
        rabbitmq_username = rabbitmq_config.get("username", "rabbitusr")
        rabbitmq_password = rabbitmq_config.get("password", "1lqgEJU3VPyhg")
        rabbitmq_host = "localhost"
        rabbitmq_port = rabbitmq_config.get("port", 5672)
        rabbitmq_vhost = rabbitmq_config.get("vhost", "/")

        broker_url = f"amqp://{rabbitmq_username}:{rabbitmq_password}@{rabbitmq_host}:{rabbitmq_port}{rabbitmq_vhost}"

    postgresql_config = config.get("postgresql")
    if postgresql_config:
        postgresql_username = postgresql_config.get("username", "postgres")
        postgresql_password = postgresql_config.get("password", "4GfW42eVb")
        postgresql_host = "localhost"
        postgresql_port = postgresql_config.get("port", 5432)
        postgresql_database = postgresql_config.get("database", "object_detection")

        result_broker_url = f"db+postgresql://{postgresql_username}:{postgresql_password}@{postgresql_host}:{postgresql_port}/{postgresql_database}"

    if broker_url != "" and result_broker_url != "":
        celery_app.conf.update(
            broker_url=broker_url,
            result_backend=result_broker_url,
            task_serializer="json",
            accept_content=["json"],
            result_serializer="json",
            timezone="UTC",
            enable_utc=True,
            task_routes={
                "app.celery_app.process_video": {"queue": "video_processing"}
            },
            worker_prefetch_multiplier=config.get("worker.prefetch_multiplier", 1),
            worker_concurrency=config.get("worker.concurrency", 4),
            worker_max_tasks_per_child=config.get("worker.max_tasks_per_child", 1000)
        )

        return celery_app
    else:
        return None

celery_app = make_celery()

class QuietTask(Task):
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        print(f"[Celery] Task {self.name} failed: {exc}")

@celery_app.task(base=QuietTask, bind=True)
def process_video(self, job_uuid: str, task_params_dict: dict) -> dict:
    try:
        task_params = TaskParameters(**task_params_dict)
    except Exception as e:
        error_msg = f"Failed to prepare task parameters: {str(e)}"
        logger.error(f"Error processing video for UUID {job_uuid} {error_msg}")

        job_tracker.update_job_state(uuid, "taskParameters", "failed")
        step40_sendResult_run(task_params, None, "failed", error_msg)

    job_tracker.update_job_state(job_uuid, "process", "started", self.request.id)

    temp_base_dir = config.get("file_processing.video_temp_directory", "/tmp/object-detection")
    video_path = os.path.join(temp_base_dir, f"{job_uuid}.video.mp4")
    
    try:
        # Download video with error handling
        try:
            step10.downloadfile_run(job_uuid, task_params.step10_downloadFile.resource_url, video_path)
        except Exception as e:
            error_msg = f"Failed to download video from {task_params.step10_downloadFile.resource_url}: {str(e)}"
            logger.error(f"Error processing video for UUID {job_uuid}: {error_msg}")

            # Update job state to failed
            job_tracker.update_job_state(job_uuid, "downloafile", "failed")

            # Send error to callback URL
            step40_sendResult_run(task_params, None, "failed", error_msg)

            # Re-raise the exception so Celery can handle retries
            raise

        # # Validate video file
        # try:
        #     validate_video_file(video_path)
        # except Exception as e:
        #     error_msg = f"Video validation failed for {task_params.step10_downloadFile.resource_url}: {str(e)}"
        #     logger.error(f"Error processing video for UUID {job_uuid}: {error_msg}")
        #
        #     # Update job state to failed
        #     job_tracker.update_job_state(job_uuid, "validation", "failed")
        #
        #     # Send error to callback URL
        #     step40_sendResult_run(task_params, None, "failed", error_msg)
        #
        #     # Re-raise the exception so Celery can handle retries
        #     raise
        #
        # # Process video
        # try:
        #     detection_result = process_video_content(
        #         video_path, task_params
        #     )
        # except Exception as e:
        #     error_msg = f"Video processing failed for {task_params.step10_downloadFile.resource_url}: {str(e)}"
        #     logger.error(f"Error processing video for UUID {job_uuid}: {error_msg}")
        #
        #     # Update job state to failed
        #     job_tracker.update_job_state(job_uuid, "process", "failed")
        #
        #     # Send error to callback URL
        #     step40_sendResult_run(task_params, None, "failed", error_msg)
        #
        #     # Re-raise the exception so Celery can handle retries
        #     raise
        #
        # # Aggregate results
        # try:
        #     aggregated_result = aggregate_results(
        #         detection_result,
        #         task_params.ot_trace_id,
        #         task_params.ot_span_id
        #     )
        # except Exception as e:
        #     error_msg = f"Failed to aggregate results for UUID {job_uuid}: {str(e)}"
        #     logger.error(f"Error processing video for UUID {job_uuid}: {error_msg}")
        #
        #     # Update job state to failed
        #     job_tracker.update_job_state(job_uuid, "aggregate", "failed")
        #
        #     # Send error to callback URL
        #     step40_sendResult_run(task_params, None, "failed", error_msg)
        #
        #     # Re-raise the exception so Celery can handle retries
        #     raise


        # Send results to callback URL
        try:
            aggregated_result = aggregate_results(
                {},
                task_params.ot_trace_id,
                task_params.ot_span_id
            )
            step40_sendResult_run(task_params, aggregated_result, "success", None)
        except Exception as e:
            error_msg = f"Failed to send results to callback URL for UUID {job_uuid}: {str(e)}"
            logger.error(f"Error processing video for UUID {job_uuid}: {error_msg}")

            job_tracker.update_job_state(job_uuid, "sendresult", "failed")

            # Send error to callback URL
            aggregated_error_result = aggregate_results(
                {},
                task_params.ot_trace_id,
                task_params.ot_span_id
            )
            step40_sendResult_run(task_params, aggregated_error_result, "failed", error_msg)
            raise

        try:
            step50_cleanEnvironment_run(task_params, video_path)
        except Exception as e:
            error_msg = f"Failed to clean up environment for UUID {job_uuid}: {str(e)}"
            logger.error(f"Error processing video for UUID {job_uuid}: {error_msg}")

            job_tracker.update_job_state(job_uuid, "sendresult", "failed")
            raise

        # Update job state to completed
        job_tracker.update_job_state(job_uuid, "process","completed")

        return {"status": "success", "uuid": job_uuid}

    except Exception as e:
        logger.error(f"Error processing video for UUID {job_uuid}: {str(e)}")
        logger.error(traceback.format_exc())

        job_tracker.update_job_state(job_uuid, "process", "failed")
        raise

    finally:
        try:
            if os.path.exists(video_path):
                os.remove(video_path)
                logger.debug(f"Cleaned up temporary directory for UUID: {job_uuid}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory for UUID {job_uuid}: {str(e)}")

def validate_video_file(video_path: str):
    """
    Validate that the file is an MP4 video.

    Args:
        video_path: Path to the video file
    """
    if not config.get("file_validation.enabled", True):
        return

    logger.info(f"Validating video file: {video_path}")

    # Check if file exists
    if not os.path.exists(video_path):
        raise ValueError("Video file does not exist")

    # Check file size
    max_size_mb = config.get("file_validation.max_file_size_mb", 1024)
    file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
    if file_size_mb > max_size_mb:
        raise ValueError(f"Video file too large: {file_size_mb:.2f} MB (max {max_size_mb} MB)")

    # Check file type using magic
    allowed_types = config.get("file_validation.allowed_content_types", ["video/mp4"])
    try:
        file_type = magic.from_file(video_path, mime=True)
    except Exception as e:
        raise ValueError(f"Failed to determine file type: {str(e)}")

    if file_type not in allowed_types:
        raise ValueError(f"Invalid file type: {file_type} (allowed: {allowed_types})")

    # Check file extension
    allowed_extensions = config.get("file_validation.allowed_extensions", [".mp4"])
    file_extension = os.path.splitext(video_path)[1].lower()
    if file_extension not in allowed_extensions:
        raise ValueError(f"Invalid file extension: {file_extension} (allowed: {allowed_extensions})")

    logger.info(f"Video file validation passed: {file_type}, {file_size_mb:.2f} MB")

def process_video_content(video_path: str, task_params: TaskParameters) -> dict:
    """
    Process video content with all enabled detection methods.

    Args:
        video_path: Path to the video file
        task_params: Task parameters

    Returns:
        dict: Detection results
    """
    logger.info(f"Processing video content for UUID: {task_params.uuid}")

    # Initialize result dictionary
    result = {
        "uuid": task_params.uuid,
        "timestamp": datetime.utcnow().isoformat(),
        "video_url": task_params.step10_downloadFile.resource_url,
        "objects": [],
        "faces": [],
        "license_plates": [],
        "barcodes": []
    }

    # Object detection
    try:
        logger.info(f"Performing object detection for UUID: {task_params.uuid}")
        logger.info(f"Performing object detection for video_path: {video_path}")
        detection_classes = config.get("yolo.detection_classes")
        logger.info(f"Performing object detection for detection_classes: {detection_classes}")
        logger.info(f"Performing object detection for confidence_threshold: {task_params.object_detection_confidence_threshold}")
        device = config.get("yolo.device", "cpu")
        logger.info(f"Performing object detection for device: {device}")
        objects = detect_objects(
            video_path,
            detection_classes=config.get("yolo.detection_classes"),
            confidence_threshold=task_params.object_detection_confidence_threshold,
            device=device
        )
        result["objects"] = objects
    except Exception as e:
        logger.error(f"Object detection failed: {str(e)}")
        raise

    # Face recognition
    if task_params.face_recognition_enabled and config.get("face_recognition.enabled", True):
        try:
            logger.info(f"Performing face recognition for UUID: {task_params.uuid}")
            faces = recognize_faces(
                video_path,
                confidence_threshold=task_params.face_recognition_confidence_threshold
            )
            result["faces"] = faces
        except Exception as e:
            logger.error(f"Face recognition failed: {str(e)}")
            raise

    # License plate recognition
    if task_params.license_plate_recognition_enabled and config.get("license_plate_recognition.enabled", True):
        try:
            logger.info(f"Performing license plate recognition for UUID: {task_params.uuid}")
            license_plates = recognize_license_plates(
                video_path,
                confidence_threshold=task_params.license_plate_recognition_confidence_threshold
            )
            result["license_plates"] = license_plates
        except Exception as e:
            logger.error(f"License plate recognition failed: {str(e)}")
            raise

    # Barcode recognition
    if task_params.barcode_recognition_enabled and config.get("barcode_recognition.enabled", True):
        try:
            logger.info(f"Performing barcode recognition for UUID: {task_params.uuid}")
            barcodes = recognize_barcodes(
                video_path,
                confidence_threshold=task_params.barcode_recognition_confidence_threshold
            )
            result["barcodes"] = barcodes
        except Exception as e:
            logger.error(f"Barcode recognition failed: {str(e)}")
            raise
    
    return result

if __name__ == "__main__":
    celery_app.start()