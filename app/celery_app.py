from celery import Celery
from app.config.settings import config
import logging

# Set up logging
logging.basicConfig(level=config.get("logging.level", "INFO"))
logger = logging.getLogger(__name__)

def make_celery():
    celery_app = Celery("object_detection_worker")

    broker_url = ""
    result_broker_url = ""

    rabbitmq_config = config.get("rabbitmq")
    if rabbitmq_config:
        rabbitmq_username = rabbitmq_config.get("username", "rabbitusr")
        rabbitmq_password = rabbitmq_config.get("password", "1lqgEJU3VPyhg")
        # rabbitmq_host = rabbitmq_config.get("host", "rabbitmq")
        rabbitmq_host = "localhost"
        rabbitmq_port = rabbitmq_config.get("port", 5672)
        rabbitmq_vhost = rabbitmq_config.get("vhost", "/")

        broker_url = f"amqp://{rabbitmq_username}:{rabbitmq_password}@{rabbitmq_host}:{rabbitmq_port}{rabbitmq_vhost}"

    mongodb_config = config.get("mongodb")
    if mongodb_config:
        mongodb_username = mongodb_config.get("username", "mongousr")
        mongodb_password = mongodb_config.get("password", "4GfW42eVb")
        # mongodb_host = mongodb_config.get("host", "mongodb")
        mongodb_host = "localhost"
        mongodb_port = mongodb_config.get("port", 27017)

        result_broker_url = f"mongodb://{mongodb_username}:{mongodb_password}@{mongodb_host}:{mongodb_port}"

    if broker_url != "" and result_broker_url != "":
        celery_app.conf.update(
            broker_url=broker_url,
            result_backend=result_broker_url,
            task_serializer="json",
            accept_content=["json"],
            result_serializer="json",
            timezone="UTC",
            enable_utc=True,
            task_routes={
                "app.tasks.process_video": {"queue": "video_processing"}
            },
            worker_prefetch_multiplier=config.get("worker.prefetch_multiplier", 1),
            worker_concurrency=config.get("worker.concurrency", 4),
            worker_max_tasks_per_child=config.get("worker.max_tasks_per_child", 1000)
        )

        return celery_app
    else:
        return None

celery_app = make_celery()

if __name__ == "__main__":
    celery_app.start()