from typing import List, Dict, Any, Tuple
from app.config.settings import config
import logging
import cv2
import numpy as np
from ultralytics import YOLO
import torch
import os

# Set up logging
from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

class ObjectDetector:
    """YOLO-based object detector."""
    
    def __init__(self):
        """Initialize the object detector."""
        self.model = None
        self.device = None
        self.detection_classes = None
        self.confidence_threshold = None
        self.nms_threshold = None
        self.process_every_n_frames = None
        
    def load_model(self, model_path: str = None, device: str = None, detection_classes: List[str] = None, 
                   confidence_threshold: float = None, nms_threshold: float = None, 
                   process_every_n_frames: int = None):
        """
        Load the YOLO model.
        
        Args:
            model_path: Path to the YOLO model file
            device: Device to run the model on ("cpu" or "cuda")
            detection_classes: List of classes to detect
            confidence_threshold: Minimum confidence threshold for detections
            nms_threshold: Non-maximum suppression threshold
            process_every_n_frames: Process every Nth frame
        """
        # Use configuration values if not provided
        model_path = model_path or config.get("yolo.model", "yolov8n.pt")
        self.device = device or config.get("yolo.device", "cpu")
        self.detection_classes = detection_classes or config.get("yolo.detection_classes", ["person", "car", "truck"])
        self.confidence_threshold = confidence_threshold or config.get("yolo.confidence_threshold", 0.5)
        self.nms_threshold = nms_threshold or config.get("yolo.nms_threshold", 0.4)
        self.process_every_n_frames = process_every_n_frames or config.get("yolo.process_every_n_frames", 5)
        
        # Check if CUDA is available if device is set to cuda
        if self.device == "cuda" and not torch.cuda.is_available():
            logger.warning("CUDA not available, falling back to CPU")
            self.device = "cpu"
        
        try:
            logger.info(f"Loading YOLO model: {model_path} on device: {self.device}")
            self.model = YOLO(model_path)
            logger.info("YOLO model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load YOLO model: {str(e)}")
            raise
    
    def detect_objects(self, video_path: str) -> List[Dict[str, Any]]:
        """
        Detect objects in a video.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of detected objects with timestamps
        """

        import time
        time.sleep(50)
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        logger.info(f"Starting object detection on video: {video_path}")
        
        # Open video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Failed to open video file: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        logger.info(f"Video properties - FPS: {fps}, Total frames: {total_frames}")
        
        detected_objects = []
        frame_count = 0
        
        try:
            while True:
                # Read frame
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Process every Nth frame
                if frame_count % self.process_every_n_frames != 0:
                    frame_count += 1
                    continue
                
                # Calculate timestamp
                timestamp = frame_count / fps
                
                # Detect objects in frame
                results = self.model(frame, device=self.device, classes=self.detection_classes, 
                                   conf=self.confidence_threshold, iou=self.nms_threshold)
                
                # Process results
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            # Get class name
                            class_id = int(box.cls[0])
                            class_name = self.model.names[class_id]
                            
                            # Get confidence
                            confidence = float(box.conf[0])
                            
                            # Get bounding box coordinates
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            width = x2 - x1
                            height = y2 - y1
                            
                            # Add to detected objects
                            detected_objects.append({
                                "class_name": class_name,
                                "confidence": confidence,
                                "bounding_box": {
                                    "x": float(x1),
                                    "y": float(y1),
                                    "width": float(width),
                                    "height": float(height)
                                },
                                "frame_number": frame_count,
                                "timestamp": timestamp
                            })
                
                frame_count += 1
                
                # Log progress
                if frame_count % 100 == 0:
                    logger.info(f"Processed {frame_count}/{total_frames} frames")
        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            error_msg = f">>>>>>>detect_objects file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"        
        finally:
            # Release video capture
            cap.release()
        
        logger.info(f"Object detection completed. Found {len(detected_objects)} objects")
        return detected_objects

# Global object detector instance
object_detector = ObjectDetector()

def detect_objects(video_path: str, detection_classes: List[str] = None, 
                   confidence_threshold: float = None, device: str = None) -> List[Dict[str, Any]]:
    """
    Detect objects in a video using YOLO.
    
    Args:
        video_path: Path to the video file
        detection_classes: List of classes to detect
        confidence_threshold: Minimum confidence threshold for detections
        device: Device to run the model on ("cpu" or "cuda")
        
    Returns:
        List of detected objects with timestamps
    """
    # Load model if not already loaded
    if object_detector.model is None:
        object_detector.load_model(
            detection_classes=detection_classes,
            confidence_threshold=confidence_threshold,
            device=device
        )
    
    # Detect objects
    return object_detector.detect_objects(video_path)