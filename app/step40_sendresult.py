from app.models import TaskParameters, AggregatedDetectionResult
from app.job_tracking import job_tracker
import logging, json
import requests

from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

def send_results_to_callback(task_params: TaskParameters, result: AggregatedDetectionResult, status: str,
                             error: str = None):
    """
    Send processing results to the callback URL.

    Args:
        task_params: Task parameters
        result: Processing result
        status: Processing status ("success" or "failed")
        error: Error message if status is "failed"
    """
    job_tracker.update_job_state(task_params.uuid, "sendresult", "started")
    try:
        if status == "failed":
            payload = {
                "ot": {
                    "trace_id": task_params.ot_trace_id,
                    "span_id": task_params.ot_span_id
                },
                "status": 0,
                "detected": result,
                "uuid": task_params.uuid,
                "error": error
            }
        else:
            payload = {
                "ot": {
                    "trace_id": task_params.ot_trace_id,
                    "span_id": task_params.ot_span_id
                },
                "status": 1,
                "detected": result,
                "uuid": task_params.uuid
            }

        # Send POST request to callback URL
        url = task_params.response_callback
        print("===========")
        print(payload)
        print("===========")
        jsonResult = json.dumps(payload)
        headers = {'Content-Type': 'application/json'}
        response = requests.post(url, data=jsonResult, headers=headers)
        response.raise_for_status()
        if response.ok:
            logger.info(f"Sending result to: {url}, jsonResult: {jsonResult} status_code:{response.status_code}")
            job_tracker.update_job_state(task_params.uuid, "sendresult", "finished")
        else:
            logger.error(f"Sending result to: {url}, jsonResult: {jsonResult} status_code:{response.status_code}")
            job_tracker.update_job_state(task_params.uuid, "sendresult", "failed")

    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to send results to callback URL: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error while sending results to callback URL: {str(e)}")