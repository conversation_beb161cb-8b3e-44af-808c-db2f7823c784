from app.models import TaskParameters, AggregatedDetectionResult
from app.job_tracking import job_tracker
from datetime import datetime
from decimal import Decimal
import logging, json
import requests
from requests.exceptions import (
    RequestException,
    ConnectionError,
    HTTPError,
    Timeout,
    TooManyRedirects
)

from app.config.logging_config import setup_logging
setup_logging()
logger = logging.getLogger(__name__)

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle special types."""
    def default(self, obj):
        if hasattr(obj, 'dict'):
            # Pydantic model
            return obj.dict()
        elif hasattr(obj, '__dict__'):
            # Regular object
            return obj.__dict__
        elif isinstance(obj, datetime):
            # DateTime objects
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            # Decimal objects
            return float(obj)
        elif hasattr(obj, 'tolist'):
            # NumPy arrays
            return obj.tolist()
        return super().default(obj)

def step40_sendResult_run(task_params: TaskParameters, result: AggregatedDetectionResult, status: str,
                             error: str = None):
    job_tracker.update_job_state(task_params.uuid, "sendresult", "started")
    try:
        # Convert result to dictionary if it's a Pydantic model
        result_dict = None
        if result is not None:
            if hasattr(result, 'dict'):
                # Pydantic model - convert to dict
                result_dict = result.dict()
            elif hasattr(result, '__dict__'):
                # Regular object - convert to dict
                result_dict = result.__dict__
            else:
                # Already a dict or primitive type
                result_dict = result

        if status == "failed":
            payload = {
                "ot": {
                    "trace_id": task_params.ot_trace_id,
                    "span_id": task_params.ot_span_id
                },
                "status": 0,
                "detected": {},
                "uuid": task_params.uuid,
                "error": error
            }
        else:
            payload = {
                "ot": {
                    "trace_id": task_params.ot_trace_id,
                    "span_id": task_params.ot_span_id
                },
                "status": 1,
                "detected": result_dict,
                "uuid": task_params.uuid
            }

        url = task_params.response_callback
        jsonResult = json.dumps(payload, cls=CustomJSONEncoder, ensure_ascii=False, indent=2)
        headers = {'Content-Type': 'application/json'}
        response = requests.post(url, data=jsonResult, headers=headers)
        response.raise_for_status()
        if response.ok:
            logger.debug(f"Sending result to: {url}, jsonResult: {jsonResult} status_code:{response.status_code}")
            logger.info(f"Sending result to: {url}, status_code:{response.status_code}")
            job_tracker.update_job_state(task_params.uuid, "sendresult", "finished")
        else:
            logger.error(f"Sending result to: {url}, jsonResult: {jsonResult} status_code:{response.status_code}")
            job_tracker.update_job_state(task_params.uuid, "sendresult", "failed")
    except Timeout as e:
        raise Exception(f"Failed to send results to callback timeout {str(e)}")
    except ConnectionError as e:
        raise Exception(f"Failed to send results to callback connection error: {str(e)}")
    except HTTPError as e:
        raise Exception(f"Failed to send results to callback http error: {str(e)}")
    except RequestException as e:
        raise Exception(f"Failed to send results to callback URL: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error while sending results to callback URL: {str(e)}")