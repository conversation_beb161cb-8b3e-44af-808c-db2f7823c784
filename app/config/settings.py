import os
import yaml
from typing import Dict, Any

# Default configuration
DEFAULT_CONFIG = {
    "rabbitmq": {
        "host": "rabbitmq",
        "port": 5672,
        "username": "rabbitusr",
        "password": "1lqgEJU3VPyhg",
        "vhost": "/"
    },
    "postgresql": {
        "host": "postgresql",
        "port": 5432,
        "username": "postgres",
        "password": "4GfW42eVb",
        "database": "object_detection",
        "echo": False
    },
    "auth": {
        "enabled": False,
        "secret_key": "",
        "algorithm": "HS256",
        "allowed_issuers": ["ezlo-auth-service"],
        "token_settings": {
            "min_duration_minutes": 5,
            "max_duration_hours": 24,
            "required_claims": ["sub", "iss", "iat", "exp"],
            "verify_options": {
                "verify_signature": True,
                "verify_exp": True,
                "verify_iat": True,
                "verify_nbf": True
            }
        }
    },
    "yolo": {
        "enabled": True,
        "model": "yolov8n.pt",
        "detection_classes": ["person", "car", "truck"],
        "confidence_threshold": 0.5,
        "nms_threshold": 0.4,
        "process_every_n_frames": 5,
        "device": "cpu"
    },
    "face_recognition": {
        "enabled": False,
        "model": "hog",
        "tolerance": 0.6,
        "upsample_times": 1,
        "num_jitters": 1,
        "known_faces_directory": "known_faces",
        "encodings_file": "known_faces/encodings.json",
        "detect_faces_only": False,
        "process_every_n_frames": 10
    },
    "license_plate_recognition": {
        "enabled": False,
        "ocr_library": "easyocr",
        "languages": ["en"],
        "detection_model": "best",
        "confidence_threshold": 0.5,
        "whitelist_characters": "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "preprocess_enabled": True,
        "preprocess_options": {
            "grayscale": True,
            "blur": False,
            "threshold": True
        },
        "process_every_n_frames": 15
    },
    "barcode_recognition": {
        "enabled": False,
        "library": "pyzbar",
        "formats": ["QR_CODE", "CODE128", "EAN13", "CODE39"],
        "confidence_threshold": 0.8,
        "preprocess_enabled": True,
        "preprocess_options": {
            "grayscale": True,
            "blur": False,
            "threshold": True
        },
        "process_every_n_frames": 20
    },
    "logging": {
        "level": "INFO",
        "format": "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s",
        "file_path": "logs/app.log",
        "max_file_size_mb": 10,
        "backup_count": 5
    },
    "job_tracking": {
        "enabled": True,
        "cleanup_interval_hours": 24,
        "retention_days": 7,
        "history_retention_days": 30
    },
    "api": {
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,
        "workers": 1
    },
    "worker": {
        "concurrency": 4,
        "prefetch_multiplier": 1,
        "max_tasks_per_child": 1000,
        "heartbeat_interval": 30,
        "status_report_interval": 60
    },
    "opentelemetry": {
        "enabled": True,
        "service_name": "object-detection-service",
        "collector_endpoint": "http://localhost:4317",
        "sampling_rate": 1.0
    },
    "file_validation": {
        "enabled": True,
        "max_file_size_mb": 1024,
        "timeout_seconds": 30,
        "allowed_content_types": ["video/mp4", "video/x-m4v"],
        "allowed_extensions": [".mp4", ".m4v"],
        "temp_directory": "/tmp/object-detection"
    },
    "monitoring": {
        "enabled": True,
        "cache_ttl_seconds": 5,
        "default_page_size": 50,
        "max_page_size": 100,
        "rate_limit_rpm": 60,
        "authentication_required": False,
        "worker_heartbeat_timeout": 60
    }
}

class Config:
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        # Start with default config
        config = DEFAULT_CONFIG.copy()
        
        # Load config from file if it exists
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                file_config = yaml.safe_load(f)
                if file_config:
                    config = self._deep_merge(config, file_config)
        
        return config
    
    def _deep_merge(self, base: Dict, override: Dict) -> Dict:
        """Deep merge two dictionaries."""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                base[key] = self._deep_merge(base[key], value)
            else:
                base[key] = value
        return base
    
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'rabbitmq.host')."""
        keys = key_path.split('.')
        value = self.config
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

# Global config instance
config = Config(config_path="config.yaml")