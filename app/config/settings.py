import os
import yaml
from typing import Dict, Any

# Default configuration
DEFAULT_CONFIG = {
    "rabbitmq": {
        "host": "localhost",
        "port": 5672,
        "username": "guest",
        "password": "guest",
        "vhost": "/"
    },
    "auth": {
        "enabled": False,
        "secret_key": "0968eb8c20c69225f15aa77d88d1552b4865b75a48ecc7289bd43c2293aac918829bfda206f38ae8ab669e6afad01c7f245f2c55bfe1c30f56e7ab67385dbb4a",
        "algorithm": "HS256",
        "token_expire_minutes": 30
    },
    "yolo": {
        "enabled": True,
        "model": "yolov8n.pt",
        "detection_classes": ["person", "car", "truck"],
        "confidence_threshold": 0.5,
        "nms_threshold": 0.4,
        "process_every_n_frames": 5,
        "device": "cpu"
    },
    "face_recognition": {
        "enabled": False,
        "model": "hog",
        "tolerance": 0.6,
        "upsample_times": 1,
        "num_jitters": 1,
        "known_faces_directory": "known_faces",
        "encodings_file": "known_faces/encodings.json",
        "detect_faces_only": False,
        "process_every_n_frames": 10
    },
    "license_plate_recognition": {
        "enabled": False,
        "ocr_library": "easyocr",
        "languages": ["en"],
        "detection_model": "best",
        "confidence_threshold": 0.5,
        "whitelist_characters": "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "preprocess_enabled": True,
        "preprocess_options": {
            "grayscale": True,
            "blur": False,
            "threshold": True
        },
        "process_every_n_frames": 15
    },
    "barcode_recognition": {
        "enabled": False,
        "library": "pyzbar",
        "formats": ["QR_CODE", "CODE128", "EAN13", "CODE39"],
        "confidence_threshold": 0.8,
        "preprocess_enabled": True,
        "preprocess_options": {
            "grayscale": True,
            "blur": False,
            "threshold": True
        },
        "process_every_n_frames": 20
    },
    "image_export": {
        "enabled": False,
        "s3": {
            "endpoint_url": "https://s3.amazonaws.com",
            "access_key_id": "your-access-key",
            "secret_access_key": "your-secret-key",
            "region_name": "us-east-1",
            "bucket_name": "object-detection-images",
            "path_prefix": "detections"
        },
        "image": {
            "format": "JPEG",
            "quality": 85,
            "max_width": 800,
            "max_height": 600,
            "padding": 20
        },
        "url": {
            "expiration_hours": 24,
            "public_access": True
        }
    },
    "logging": {
        "level": "INFO",
        "format": "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s",
        "file_path": "logs/app.log",
        "max_file_size_mb": 10,
        "backup_count": 5
    },
    "job_tracking": {
        "enabled": True,
        "cleanup_interval_hours": 24,
        "retention_days": 7,
        "history_retention_days": 30
    },
    "api": {
        "host": "0.0.0.0",
        "port": 8000,
        "reload": True,
        "workers": 1
    },
    "worker": {
        "concurrency": 4,
        "prefetch_multiplier": 1,
        "max_tasks_per_child": 1000,
        "heartbeat_interval": 30,
        "status_report_interval": 60
    },
    "opentelemetry": {
        "enabled": True,
        "service_name": "object-detection-service",
        "collector_endpoint": "http://localhost:4317",
        "sampling_rate": 1.0
    },
    "file_validation": {
        "enabled": True,
        "max_file_size_mb": 1024,
        "timeout_seconds": 30,
        "allowed_content_types": ["video/mp4", "video/x-m4v"],
        "allowed_extensions": [".mp4", ".m4v"],
        "temp_directory": "/tmp/object-detection"
    },
    "monitoring": {
        "enabled": True,
        "cache_ttl_seconds": 5,
        "default_page_size": 50,
        "max_page_size": 100,
        "rate_limit_rpm": 60,
        "authentication_required": False,
        "worker_heartbeat_timeout": 60
    }
}

class Config:
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        # Start with default config
        config = DEFAULT_CONFIG.copy()
        
        # Load config from file if it exists
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                file_config = yaml.safe_load(f)
                if file_config:
                    config = self._deep_merge(config, file_config)
        
        return config
    
    def _deep_merge(self, base: Dict, override: Dict) -> Dict:
        """Deep merge two dictionaries."""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                base[key] = self._deep_merge(base[key], value)
            else:
                base[key] = value
        return base
    
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'rabbitmq.host')."""
        keys = key_path.split('.')
        value = self.config
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

# Global config instance
config = Config(config_path="config.yaml")