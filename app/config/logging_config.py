import logging
from logging.handlers import RotatingFileHandler
from app.config.settings import config

def setup_logging():
    """Configure logging for the entire application."""
    # Get logging configuration from settings
    log_level = config.get("logging.level", "INFO")
    log_format = config.get("logging.format", "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s")
    log_file_path = config.get("logging.file_path")
    max_file_size_mb = config.get("logging.max_file_size_mb", 10)
    backup_count = config.get("logging.backup_count", 5)

    # Create formatter
    formatter = logging.Formatter(log_format)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Remove existing handlers to avoid duplicate logs
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # Add file handler if file path is specified
    if log_file_path:
        try:
            file_handler = RotatingFileHandler(
                log_file_path,
                maxBytes=max_file_size_mb * 1024 * 1024,
                backupCount=backup_count
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            console_handler.setLevel(logging.WARNING)
            root_logger.warning(f"Failed to setup file logging: {e}")

    return root_logger