#!/bin/bash

# Setup script for Object Detection Service

echo "Setting up Object Detection Service..."

# Check if Python 3.11 is installed
if ! command -v python3.11 &> /dev/null
then
    echo "Python 3.11 is not installed. Please install Python 3.11 and try again."
    exit 1
fi

# Create virtual environment
echo "Creating virtual environment..."
python3.11 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt



# Create known_faces directory
echo "Creating known_faces directory..."
mkdir -p known_faces

echo "Setup complete!"
echo ""
echo "To run the application:"
echo "1. Start required services (RabbitMQ and MongoDB)"
echo "2. Activate virtual environment: source venv/bin/activate"
echo "3. Start FastAPI app: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"
echo "4. In another terminal, start Celery worker: celery -A app.celery_app worker --loglevel=info --queues=video_processing"
echo ""
echo "See HOW_TO_RUN.md for detailed instructions."